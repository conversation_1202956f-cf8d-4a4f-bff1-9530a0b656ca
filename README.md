# 🚀 Nova - Space Technology Learning Platform

Nova is Africa's premier platform for space technology education, mentorship, and career opportunities. Built with Next.js, MongoDB, and powered by AI, Nova empowers the next generation of space technology innovators across Africa.

## ✨ Features

### 🎓 Learning Hub
- **200+ Expert Courses** covering space technology, satellite engineering, AI, cybersecurity, and more
- **Interactive Content** with videos, quizzes, and hands-on projects
- **Progress Tracking** with detailed analytics and learning paths
- **Industry Certifications** with blockchain-verified credentials

### 👥 Mentorship System
- **AI-Powered Matching** connecting mentees with industry experts
- **Scheduled Sessions** with integrated booking and reminders
- **Video/Voice Chat** integration for seamless communication
- **Progress Monitoring** and goal tracking

### 💼 Job & Apprenticeship Board
- **Curated Opportunities** from leading space technology companies
- **Smart Filtering** by location, experience level, and skills
- **Application Tracking** with status updates
- **Interview Scheduling** with SMS notifications

### 🏆 Certification Engine
- **Skill Assessments** with AI-powered evaluation
- **Digital Certificates** with blockchain verification
- **Micro-credentials** for specific competencies
- **Portfolio Building** tools

### 📅 Events Hub
- **Webinars & Workshops** with industry leaders
- **Job Fairs** and networking events
- **Competitions** and hackathons
- **Community Meetups** across Africa

### 💬 Community Space
- **Discussion Forums** for knowledge sharing
- **Project Collaboration** tools
- **Peer Learning** groups
- **Expert Q&A** sessions

### 🤖 AI-Powered Features
- **Personalized Learning Paths** based on goals and skills
- **Smart Mentor Matching** using advanced algorithms
- **Job Recommendations** tailored to user profiles
- **Skill Gap Analysis** with improvement suggestions

### 📱 SMS Integration (Africa's Talking)
- **Course Reminders** and notifications
- **Job Alerts** for matching opportunities
- **Event Notifications** and updates
- **Mentorship Booking** confirmations
- **Certificate Notifications** when earned

## 🛠 Tech Stack

### Frontend
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Query** for data fetching
- **React Hook Form** for form handling

### Backend
- **Next.js API Routes** for serverless functions
- **MongoDB** with Mongoose ODM
- **NextAuth.js** for authentication
- **JWT** for session management

### AI & External Services
- **Google Gemini AI** for personalization and recommendations
- **Africa's Talking SMS API** for notifications
- **Cloudinary** for media storage (planned)

### Development Tools
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **pnpm** for package management

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm/yarn
- MongoDB database
- Africa's Talking account
- Google Gemini AI API key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/nova.git
cd nova
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Set up environment variables**
Create a `.env.local` file in the root directory:
```env
# Database Configuration
MONGODB_URI=

# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_ENDPOINT=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent

# Africa's Talking Configuration
AFRICASTALKING_USERNAME=your_username_here
AFRICASTALKING_API_KEY=your_api_key_here

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here

# App Configuration
NODE_ENV=development
```

4. **Run the development server**
```bash
pnpm dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
nova/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   ├── courses/           # Courses pages
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Homepage
│   │   └── providers.tsx      # Context providers
│   ├── components/            # React components
│   │   ├── layout/           # Layout components
│   │   └── sections/         # Page sections
│   ├── lib/                  # Utility libraries
│   │   ├── mongodb.ts        # Database connection
│   │   ├── sms.ts           # SMS service
│   │   └── gemini.ts        # AI service
│   └── models/              # Database models
│       ├── User.ts
│       ├── Course.ts
│       ├── Job.ts
│       └── Enrollment.ts
├── public/                  # Static assets
├── .env.local              # Environment variables
├── package.json            # Dependencies
└── README.md              # This file
```

## 🔐 Roles & Permissions

Nova implements a comprehensive role-based access control (RBAC) system:

| Role | Permissions | Key Features |
|------|-------------|--------------|
| **Admin** | Full platform access | User management, content moderation, analytics, system settings |
| **Student** | Learning & participation | Course enrollment, job applications, mentorship, community participation |
| **Mentor** | Teaching & guidance | Course creation, mentorship sessions, student interaction, events |
| **Employer** | Recruitment & hiring | Job posting, candidate management, recruitment events, analytics |
| **Partner** | Regional collaboration | Regional content, local events, community moderation, analytics |

### Role Application System
- **Students** can apply to become **Mentors** or **Employers**
- **Mentors** can apply to become **Partners** (with verification)
- **Admin** role requires manual assignment
- All applications go through admin approval process
- Complete audit trail of role changes

### Permission Enforcement
- **API Level:** Middleware protection on all endpoints
- **UI Level:** Role-based component rendering
- **Database Level:** Secure data access patterns

For detailed information, see [ROLES_AND_PERMISSIONS.md](./ROLES_AND_PERMISSIONS.md)

## 🔧 API Endpoints

### Courses
- `GET /api/courses` - Get all courses with filtering
- `POST /api/courses` - Create a new course
- `GET /api/courses/[id]` - Get course details
- `PUT /api/courses/[id]` - Update course
- `DELETE /api/courses/[id]` - Delete course

### Mentorship
- `GET /api/mentorship/sessions` - Get mentorship sessions
- `POST /api/mentorship/sessions` - Book a mentorship session
- `GET /api/mentorship/sessions/[id]` - Get session details
- `PUT /api/mentorship/sessions/[id]` - Update session
- `DELETE /api/mentorship/sessions/[id]` - Cancel session
- `GET /api/mentorship/mentors` - Get available mentors
- `POST /api/mentorship/mentors` - Apply to become a mentor

### Jobs & Applications
- `GET /api/jobs` - Get job listings with filtering
- `POST /api/jobs` - Create a new job posting
- `GET /api/jobs/[id]` - Get job details
- `POST /api/jobs/[id]/apply` - Apply for a job
- `GET /api/jobs/[id]/apply` - Get application status

### Events
- `GET /api/events` - Get events with filtering
- `POST /api/events` - Create a new event
- `GET /api/events/[id]` - Get event details
- `POST /api/events/[id]/register` - Register for event
- `DELETE /api/events/[id]/register` - Cancel registration

### Community Forum
- `GET /api/community/posts` - Get forum posts
- `POST /api/community/posts` - Create a new post
- `GET /api/community/posts/[id]` - Get post details
- `POST /api/community/posts/[id]/reply` - Reply to post
- `POST /api/community/posts/[id]/like` - Like/unlike post

### Certificates & Assessments
- `POST /api/certificates/generate` - Generate certificate
- `GET /api/certificates/[id]` - Get certificate details
- `GET /api/assessments` - Get user assessments
- `POST /api/assessments` - Start new assessment
- `POST /api/assessments/[id]/submit` - Submit assessment

### Resources
- `GET /api/resources` - Get resource library
- `POST /api/resources` - Upload new resource
- `GET /api/resources/[id]` - Get resource details
- `POST /api/resources/[id]/bookmark` - Bookmark resource

### Admin Dashboard
- `GET /api/admin/dashboard` - Get admin analytics
- `GET /api/admin/users` - Manage users
- `GET /api/admin/analytics` - Detailed analytics

### SMS Notifications
- `POST /api/sms/send` - Send SMS notifications
  - Types: `welcome`, `course_reminder`, `mentorship_booking`, `job_alert`, `certification`, `event_reminder`, `custom`

### AI Recommendations
- `POST /api/ai/recommendations` - Generate AI-powered recommendations
  - Types: `learning_path`, `mentor_matching`, `job_recommendations`, `skill_assessment`, `course_recommendations`

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/enroll` - Enroll in course

## 🌍 Deployment

### Environment Setup
1. **Production Database**: Set up MongoDB Atlas or your preferred MongoDB hosting
2. **Environment Variables**: Update `.env.local` with production values
3. **API Keys**: Ensure all API keys are properly configured

### Vercel Deployment (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Docker Deployment
```bash
# Build Docker image
docker build -t nova-platform .

# Run container
docker run -p 3000:3000 nova-platform
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
pnpm test

# E2E tests
pnpm test:e2e

# Test coverage
pnpm test:coverage
```

### API Testing
Use the included Postman collection or test with curl:
```bash
# Test SMS endpoint
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -d '{"to": "+************", "type": "welcome", "data": {"name": "John Doe"}}'
```

## 🤝 Contributing

We welcome contributions from the community! Please read our [Contributing Guidelines](CONTRIBUTING.md) before submitting pull requests.

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Code Standards
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

## 📊 Monitoring & Analytics

### Performance Monitoring
- **Vercel Analytics** for web vitals
- **Sentry** for error tracking (planned)
- **LogRocket** for user session recording (planned)

### Business Metrics
- User engagement and course completion rates
- SMS delivery and open rates
- Job application success rates
- Mentor-mentee matching effectiveness

## 🔒 Security

### Data Protection
- **Encryption**: All sensitive data encrypted at rest and in transit
- **Authentication**: Secure JWT-based authentication
- **Authorization**: Role-based access control
- **Privacy**: GDPR-compliant data handling

### Security Best Practices
- Regular security audits
- Dependency vulnerability scanning
- Rate limiting on API endpoints
- Input validation and sanitization

## 🌟 Roadmap

### Phase 1 (Current) - ✅ COMPLETED
- ✅ Core platform development
- ✅ Learning hub with courses and enrollment system
- ✅ Complete mentorship system with booking and AI matching
- ✅ Job board with application tracking
- ✅ Events management and registration system
- ✅ Community forum with reputation system
- ✅ Certification engine with skill assessments
- ✅ Resource library with file management
- ✅ SMS integration with Africa's Talking
- ✅ AI-powered recommendations and assessments
- ✅ User authentication system with role-based access
- ✅ Comprehensive admin dashboard with analytics

### Phase 2 (Q2 2025)
- 📱 Mobile app development
- 🎥 Video streaming integration
- 💳 Payment processing
- 🔗 Blockchain certificates
- 📊 Advanced analytics

### Phase 3 (Q3 2025)
- 🌐 Multi-language support
- 🤖 Advanced AI features
- 🏢 Enterprise partnerships
- 📡 Satellite data integration
- 🎮 Gamification features

### Phase 4 (Q4 2025)
- 🌍 Pan-African expansion
- 🚀 Space mission simulations
- 🔬 Virtual labs
- 🤝 Government partnerships
- 📈 IPO preparation

## 📞 Support

### Community Support
- **Discord**: [Join our community](https://discord.gg/nova-africa)
- **Forum**: [Community discussions](https://forum.nova.africa)
- **GitHub Issues**: [Report bugs or request features](https://github.com/nova-africa/platform/issues)

### Enterprise Support
- **Email**: <EMAIL>
- **Phone**: +254 700 000 000
- **Slack**: Enterprise customers get dedicated Slack support

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Kenya Space Agency** for technical guidance
- **Africa's Talking** for SMS infrastructure
- **Google** for AI capabilities
- **MongoDB** for database solutions
- **Vercel** for hosting platform
- **Open Source Community** for amazing tools and libraries

## 📈 Stats

![GitHub stars](https://img.shields.io/github/stars/nova-africa/platform?style=social)
![GitHub forks](https://img.shields.io/github/forks/nova-africa/platform?style=social)
![GitHub issues](https://img.shields.io/github/issues/nova-africa/platform)
![GitHub license](https://img.shields.io/github/license/nova-africa/platform)

---

**Built with ❤️ for Africa's space technology future**

*Empowering the next generation of space innovators across the continent*
