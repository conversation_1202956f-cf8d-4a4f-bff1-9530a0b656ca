# 🧪 Nova Platform - Testing Guide

## 🚀 Quick Start Testing

### 1. **Environment Setup**
```bash
# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local

# Required environment variables:
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_SECRET=your_secret_key
NEXTAUTH_URL=http://localhost:3000
GEMINI_API_KEY=your_gemini_api_key
AFRICASTALKING_USERNAME=your_at_username
AFRICASTALKING_API_KEY=your_at_api_key
```

### 2. **Start Development Server**
```bash
pnpm dev
```

### 3. **Create Admin User**
```bash
pnpm create-admin
```

## 🧪 **Feature Testing Checklist**

### ✅ **Authentication System**
- [ ] User registration with different roles
- [ ] Login/logout functionality
- [ ] Password reset flow
- [ ] Profile management
- [ ] Role-based access control

**Test URLs:**
- `/auth/signup` - User registration
- `/auth/signin` - User login
- `/dashboard` - User dashboard

### ✅ **Learning Hub**
- [ ] Browse courses with filtering
- [ ] Course enrollment
- [ ] Progress tracking
- [ ] Course completion
- [ ] Certificate generation

**Test URLs:**
- `/courses` - Course catalog
- `/courses/[id]` - Individual course
- `/dashboard` - Progress tracking

**API Tests:**
```bash
# Get courses
curl http://localhost:3000/api/courses

# Enroll in course (requires auth)
curl -X POST http://localhost:3000/api/enrollments \
  -H "Content-Type: application/json" \
  -d '{"courseId": "COURSE_ID"}'
```

### ✅ **Mentorship System**
- [ ] Browse available mentors
- [ ] Book mentorship sessions
- [ ] Session management
- [ ] Mentor profile creation
- [ ] Session feedback

**Test URLs:**
- `/mentorship` - Mentor catalog
- `/dashboard` - Session management

**API Tests:**
```bash
# Get mentors
curl http://localhost:3000/api/mentorship/mentors

# Book session (requires auth)
curl -X POST http://localhost:3000/api/mentorship/sessions \
  -H "Content-Type: application/json" \
  -d '{
    "mentorId": "MENTOR_ID",
    "title": "Test Session",
    "scheduledAt": "2024-12-25T10:00:00Z",
    "duration": 60
  }'
```

### ✅ **Job Board**
- [ ] Browse job listings
- [ ] Apply for jobs
- [ ] Application tracking
- [ ] Employer job posting
- [ ] Application management

**Test URLs:**
- `/jobs` - Job listings
- `/jobs/[id]` - Job details

**API Tests:**
```bash
# Get jobs
curl http://localhost:3000/api/jobs

# Apply for job (requires auth)
curl -X POST http://localhost:3000/api/jobs/JOB_ID/apply \
  -H "Content-Type: application/json" \
  -d '{
    "coverLetter": "Test cover letter",
    "resumeUrl": "https://example.com/resume.pdf"
  }'
```

### ✅ **Events System**
- [ ] Browse events
- [ ] Event registration
- [ ] Event creation
- [ ] Registration management
- [ ] Event reminders

**Test URLs:**
- `/events` - Event listings
- `/events/[id]` - Event details

**API Tests:**
```bash
# Get events
curl http://localhost:3000/api/events

# Register for event (requires auth)
curl -X POST http://localhost:3000/api/events/EVENT_ID/register \
  -H "Content-Type: application/json" \
  -d '{}'
```

### ✅ **Community Forum**
- [ ] Browse forum posts
- [ ] Create new posts
- [ ] Reply to posts
- [ ] Like/unlike posts
- [ ] User reputation system

**Test URLs:**
- `/community` - Forum homepage

**API Tests:**
```bash
# Get forum posts
curl http://localhost:3000/api/community/posts

# Create post (requires auth)
curl -X POST http://localhost:3000/api/community/posts \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Post",
    "content": "This is a test post",
    "category": "General Discussion",
    "tags": ["test"]
  }'
```

### ✅ **Certification Engine**
- [ ] Take skill assessments
- [ ] Generate certificates
- [ ] Certificate verification
- [ ] Skill level evaluation
- [ ] AI-powered feedback

**API Tests:**
```bash
# Start assessment (requires auth)
curl -X POST http://localhost:3000/api/assessments \
  -H "Content-Type: application/json" \
  -d '{
    "skillArea": "Satellite Engineering",
    "assessmentType": "quiz"
  }'

# Generate certificate (requires auth)
curl -X POST http://localhost:3000/api/certificates/generate \
  -H "Content-Type: application/json" \
  -d '{
    "courseId": "COURSE_ID",
    "assessmentScore": 85,
    "assessmentMaxScore": 100
  }'
```

### ✅ **Resource Library**
- [ ] Browse resources
- [ ] Upload resources
- [ ] Bookmark resources
- [ ] Resource collections
- [ ] Access control

**API Tests:**
```bash
# Get resources
curl http://localhost:3000/api/resources

# Upload resource (requires auth and file upload)
# Use form data with file upload
```

### ✅ **Admin Dashboard**
- [ ] Platform analytics
- [ ] User management
- [ ] Content moderation
- [ ] System statistics
- [ ] Approval workflows

**Test URLs:**
- `/dashboard/admin` - Admin dashboard
- `/auth/signin` - Unified login (redirects to role-specific dashboard)

**API Tests:**
```bash
# Get admin dashboard (requires admin auth)
curl http://localhost:3000/api/admin/dashboard
```

### ✅ **SMS Integration**
- [ ] Course completion notifications
- [ ] Mentorship booking confirmations
- [ ] Job application alerts
- [ ] Event reminders
- [ ] Certificate notifications

**API Tests:**
```bash
# Send SMS (requires auth)
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+254700000000",
    "type": "welcome",
    "data": {
      "userName": "Test User"
    }
  }'
```

## 🔧 **Database Testing**

### **Seed Data**

#### Basic Seeding
```bash
# Create sample data (full dataset)
curl -X POST http://localhost:3000/api/seed

# Or use the script
pnpm seed
```

#### Fast Testing Mode
For faster test execution, you can skip resource-intensive entities:

```bash
# Skip resources and mentor profiles
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "skipResources": true,
      "skipMentorProfiles": true
    }
  }'
```

#### Minimal Mode
For the fastest seeding (only users and courses):

```bash
# Environment variable approach
SEED_MINIMAL_MODE=true pnpm seed

# Or via API
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "minimalMode": true
    }
  }'
```

#### Environment Configuration
Add these to your `.env.local` for persistent configuration:

```bash
# Skip specific entities
SEED_SKIP_RESOURCES=true
SEED_SKIP_MENTOR_PROFILES=true

# Or use minimal mode
SEED_MINIMAL_MODE=true
```

### **Database Verification**
Check that all collections are created:
- users
- courses
- enrollments
- jobs
- mentorshipSessions
- mentorProfiles
- events
- eventRegistrations
- forumPosts
- forumReplies
- certificates
- skillAssessments
- resources
- jobApplications

## 🐛 **Common Issues & Solutions**

### **Database Connection**
- Ensure MongoDB is running
- Check connection string format
- Verify network access

### **Authentication Issues**
- Check NEXTAUTH_SECRET is set
- Verify NEXTAUTH_URL matches your domain
- Clear browser cookies if needed

### **SMS Not Working**
- Verify Africa's Talking credentials
- Check phone number format (+254...)
- Ensure SMS service is active

### **AI Features Not Working**
- Verify GEMINI_API_KEY is valid
- Check API quota and limits
- Review error logs for details

## 📊 **Performance Testing**

### **Load Testing**
```bash
# Install artillery for load testing
npm install -g artillery

# Create test script and run
artillery quick --count 10 --num 5 http://localhost:3000/api/courses
```

### **Database Performance**
- Monitor query execution times
- Check index usage
- Optimize slow queries

## ✅ **Production Readiness Checklist**

- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database indexes created
- [ ] SSL certificates installed
- [ ] Monitoring setup
- [ ] Backup strategy implemented
- [ ] Error logging configured
- [ ] Performance optimized

## 🎯 **Test Scenarios**

### **User Journey 1: Student Learning Path**
1. Register as student
2. Browse and enroll in course
3. Complete course modules
4. Take skill assessment
5. Generate certificate
6. Join community forum

### **User Journey 2: Mentorship Experience**
1. Register as student
2. Browse mentors
3. Book mentorship session
4. Attend session
5. Provide feedback
6. Apply to become mentor

### **User Journey 3: Job Application**
1. Register as student
2. Complete profile
3. Browse job listings
4. Apply for job
5. Track application status
6. Receive interview invitation

The platform is ready for comprehensive testing! 🚀
