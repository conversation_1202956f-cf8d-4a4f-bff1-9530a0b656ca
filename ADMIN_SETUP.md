# Nova Admin System Setup

This document explains how to set up and use the Nova platform's admin system.

## 🚀 Quick Start

### 1. Create Admin User

First, create an admin user by running the setup script:

```bash
# Install dependencies if not already done
pnpm install

# Create the admin user
pnpm create-admin
```

This will create an admin user with:
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: `admin`

⚠️ **Important**: Change the default password after first login!

### 2. Access Admin Portal

Navigate to the main login page:
```
http://localhost:3000/auth/signin
```

Use the credentials created in step 1 to log in. You will be automatically redirected to the admin dashboard based on your role.

## 🔐 Admin Features

### Authentication & Security
- **Unified Authentication**: Single login system for all users with role-based redirection
- **Role-Based Access Control**: Only users with 'admin' role can access admin areas
- **Route Protection**: Middleware automatically protects admin routes
- **Session Management**: Secure JWT-based session handling

### Admin Dashboard
- **Platform Overview**: Key metrics and statistics
- **User Analytics**: Total users, active users, new registrations
- **Course Analytics**: Course enrollments and completion rates
- **Job Analytics**: Job postings and applications
- **Quick Actions**: Direct access to management functions

### User Management
- **View All Users**: Paginated list of all platform users
- **Search & Filter**: Find users by name, email, role, or status
- **User Details**: View complete user profiles and activity
- **Role Management**: Change user roles (student, mentor, employer, admin, partner)
- **Account Status**: Activate/deactivate user accounts
- **Password Reset**: Reset user passwords as admin

## 🛠 API Endpoints

### Admin Analytics
```
GET /api/admin/analytics
```
Returns platform statistics and analytics data.

### User Management
```
GET /api/admin/users              # List all users
POST /api/admin/users             # Create new user
GET /api/admin/users/[id]         # Get user details
PUT /api/admin/users/[id]         # Update user
DELETE /api/admin/users/[id]      # Deactivate user
```

## 🔒 Security Features

### Route Protection
- Admin routes are protected by middleware
- Automatic redirect to appropriate dashboard for unauthorized access
- Session validation on every admin request

### Role Validation
- Server-side role checking on all admin API endpoints
- Client-side role validation for UI components
- Prevents privilege escalation attacks

### Admin Restrictions
- Admins cannot delete their own accounts
- Admins cannot change their own roles
- All admin actions are logged (future enhancement)

## 📁 File Structure

```
src/
├── app/dashboard/admin/
│   └── page.tsx                # Admin dashboard
├── api/admin/
│   ├── analytics/route.ts      # Analytics API
│   ├── dashboard/route.ts      # Dashboard data API
│   └── users/
│       ├── route.ts            # User list/create API
│       └── [id]/route.ts       # Individual user API
├── lib/
│   ├── auth.ts                 # Unified authentication
│   └── auth-middleware.ts      # Authentication middleware
├── middleware.ts               # Route protection middleware
└── scripts/
    └── create-admin.ts         # Admin user creation script
```

## 🎨 UI Components

### Unified Login Page
- Modern, secure design with gradient background
- Form validation and error handling
- Loading states and success feedback
- Automatic role-based redirection after login

### Admin Dashboard
- Clean, professional interface
- Real-time statistics cards
- Quick action buttons
- Recent activity feed
- Responsive design for all devices

## 🔧 Configuration

### Environment Variables
Make sure these are set in your `.env.local`:

```env
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

### Role Configuration
User roles are defined in the User model:
- `student` - Regular platform users
- `mentor` - Mentors providing guidance
- `employer` - Companies posting jobs
- `admin` - Platform administrators
- `partner` - Partner organizations

## 🚀 Deployment Notes

### Production Setup
1. Change default admin password immediately
2. Use strong, unique passwords for admin accounts
3. Enable HTTPS for all admin routes
4. Consider IP whitelisting for admin access
5. Set up monitoring and logging for admin actions

### Security Checklist
- [ ] Default admin password changed
- [ ] HTTPS enabled in production
- [ ] Strong session secrets configured
- [ ] Admin access monitoring enabled
- [ ] Regular security audits scheduled

## 🆘 Troubleshooting

### Common Issues

**Cannot access admin dashboard**
- Verify user has 'admin' role in database
- Check session is valid and not expired
- Ensure middleware is properly configured

**Admin login not working**
- Verify admin user exists in database
- Check password is correct
- Ensure MongoDB connection is working
- Verify user is being redirected to correct dashboard

**API endpoints returning 403**
- Verify user is authenticated
- Check user has admin role
- Ensure auth middleware is working properly

### Support
For additional support or questions about the admin system, please refer to the main project documentation or contact the development team.
