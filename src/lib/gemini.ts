import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export class GeminiService {
  private static model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });

  static async generatePersonalizedLearningPath(userProfile: {
    skills: string[];
    interests: string[];
    careerGoals: string[];
    currentLevel: string;
  }) {
    const prompt = `
    Based on the following user profile, create a personalized learning path for space technology and innovation:
    
    Current Skills: ${userProfile.skills.join(', ')}
    Interests: ${userProfile.interests.join(', ')}
    Career Goals: ${userProfile.careerGoals.join(', ')}
    Current Level: ${userProfile.currentLevel}
    
    Please provide:
    1. Recommended courses in order of priority
    2. Skill gaps to address
    3. Suggested mentorship areas
    4. Relevant job opportunities to target
    5. Timeline for achieving goals
    
    Focus on space technology, satellite engineering, AI in space, cybersecurity for space systems, and related fields.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating learning path:', error);
      throw error;
    }
  }

  static async matchMentorToMentee(menteeProfile: any, availableMentors: any[]) {
    const prompt = `
    Match the best mentor for this mentee based on their profile and goals:
    
    Mentee Profile:
    - Skills: ${menteeProfile.skills?.join(', ') || 'Not specified'}
    - Interests: ${menteeProfile.interests?.join(', ') || 'Not specified'}
    - Goals: ${menteeProfile.goals?.join(', ') || 'Not specified'}
    - Experience Level: ${menteeProfile.level || 'Beginner'}
    
    Available Mentors:
    ${availableMentors.map((mentor, index) => `
    ${index + 1}. ${mentor.name}
       - Expertise: ${mentor.expertise?.join(', ') || 'Not specified'}
       - Experience: ${mentor.experience || 'Not specified'}
       - Specialization: ${mentor.specialization || 'Not specified'}
    `).join('')}
    
    Provide the top 3 mentor matches with reasons for each match.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error matching mentor:', error);
      throw error;
    }
  }

  static async generateJobRecommendations(userProfile: any, availableJobs: any[]) {
    const prompt = `
    Recommend the most suitable jobs for this user based on their profile:
    
    User Profile:
    - Skills: ${userProfile.skills?.join(', ') || 'Not specified'}
    - Experience: ${userProfile.experience || 'Not specified'}
    - Interests: ${userProfile.interests?.join(', ') || 'Not specified'}
    - Education: ${userProfile.education || 'Not specified'}
    
    Available Jobs:
    ${availableJobs.map((job, index) => `
    ${index + 1}. ${job.title} at ${job.company}
       - Requirements: ${job.requirements?.join(', ') || 'Not specified'}
       - Type: ${job.type || 'Not specified'}
       - Location: ${job.location || 'Not specified'}
    `).join('')}
    
    Rank the top 5 job matches and explain why each job is suitable.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating job recommendations:', error);
      throw error;
    }
  }

  static async generateSkillAssessment(userAnswers: any[], skillArea: string) {
    const prompt = `
    Assess the user's skill level in ${skillArea} based on their quiz answers:
    
    Quiz Answers:
    ${userAnswers.map((answer, index) => `
    Question ${index + 1}: ${answer.question}
    Answer: ${answer.userAnswer}
    Correct Answer: ${answer.correctAnswer}
    `).join('')}
    
    Provide:
    1. Overall skill level (Beginner/Intermediate/Advanced)
    2. Strengths identified
    3. Areas for improvement
    4. Recommended next steps
    5. Suggested learning resources
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating skill assessment:', error);
      throw error;
    }
  }
}
