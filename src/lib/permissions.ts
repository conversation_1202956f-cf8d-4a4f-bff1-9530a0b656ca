import { IUser } from '@/models/User';

export type UserRole = 'admin' | 'student' | 'mentor' | 'organisation';

export interface Permission {
  resource: string;
  action: string;
  condition?: (user: IUser, resourceData?: any) => boolean;
}

// Define all possible permissions
export const PERMISSIONS = {
  // User Management
  MANAGE_USERS: 'users:manage',
  VIEW_USER_PROFILES: 'users:view',
  EDIT_OWN_PROFILE: 'users:edit_own',
  DELETE_USERS: 'users:delete',

  // Course Management
  CREATE_COURSES: 'courses:create',
  EDIT_COURSES: 'courses:edit',
  DELETE_COURSES: 'courses:delete',
  VIEW_COURSES: 'courses:view',
  ENROLL_COURSES: 'courses:enroll',
  MANAGE_COURSE_CONTENT: 'courses:manage_content',

  // Job Management
  CREATE_JOBS: 'jobs:create',
  EDIT_JOBS: 'jobs:edit',
  DELETE_JOBS: 'jobs:delete',
  VIEW_JOBS: 'jobs:view',
  APPLY_JOBS: 'jobs:apply',
  MANAGE_APPLICATIONS: 'jobs:manage_applications',
  VIEW_APPLICATIONS: 'jobs:view_applications',

  // Mentorship
  CREATE_MENTOR_PROFILE: 'mentorship:create_profile',
  BOOK_SESSIONS: 'mentorship:book',
  MANAGE_SESSIONS: 'mentorship:manage',
  VIEW_MENTORSHIP: 'mentorship:view',
  APPROVE_MENTORS: 'mentorship:approve',

  // Events
  CREATE_EVENTS: 'events:create',
  EDIT_EVENTS: 'events:edit',
  DELETE_EVENTS: 'events:delete',
  VIEW_EVENTS: 'events:view',
  REGISTER_EVENTS: 'events:register',
  MANAGE_EVENT_REGISTRATIONS: 'events:manage_registrations',

  // Community
  CREATE_POSTS: 'community:create_posts',
  EDIT_POSTS: 'community:edit_posts',
  DELETE_POSTS: 'community:delete_posts',
  MODERATE_COMMUNITY: 'community:moderate',
  VIEW_COMMUNITY: 'community:view',

  // Certificates
  GENERATE_CERTIFICATES: 'certificates:generate',
  VIEW_CERTIFICATES: 'certificates:view',
  MANAGE_CERTIFICATES: 'certificates:manage',

  // Resources
  UPLOAD_RESOURCES: 'resources:upload',
  EDIT_RESOURCES: 'resources:edit',
  DELETE_RESOURCES: 'resources:delete',
  VIEW_RESOURCES: 'resources:view',
  APPROVE_RESOURCES: 'resources:approve',

  // Analytics
  VIEW_ANALYTICS: 'analytics:view',
  VIEW_DETAILED_ANALYTICS: 'analytics:detailed',
  EXPORT_DATA: 'analytics:export',

  // Admin Functions
  MANAGE_PLATFORM: 'admin:manage',
  MODERATE_CONTENT: 'admin:moderate',
  MANAGE_SETTINGS: 'admin:settings',
  SEND_NOTIFICATIONS: 'admin:notifications',
} as const;

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  admin: [
    // Full access to everything
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.VIEW_USER_PROFILES,
    PERMISSIONS.EDIT_OWN_PROFILE,
    PERMISSIONS.DELETE_USERS,
    PERMISSIONS.CREATE_COURSES,
    PERMISSIONS.EDIT_COURSES,
    PERMISSIONS.DELETE_COURSES,
    PERMISSIONS.VIEW_COURSES,
    PERMISSIONS.ENROLL_COURSES,
    PERMISSIONS.MANAGE_COURSE_CONTENT,
    PERMISSIONS.CREATE_JOBS,
    PERMISSIONS.EDIT_JOBS,
    PERMISSIONS.DELETE_JOBS,
    PERMISSIONS.VIEW_JOBS,
    PERMISSIONS.APPLY_JOBS,
    PERMISSIONS.MANAGE_APPLICATIONS,
    PERMISSIONS.VIEW_APPLICATIONS,
    PERMISSIONS.CREATE_MENTOR_PROFILE,
    PERMISSIONS.BOOK_SESSIONS,
    PERMISSIONS.MANAGE_SESSIONS,
    PERMISSIONS.VIEW_MENTORSHIP,
    PERMISSIONS.APPROVE_MENTORS,
    PERMISSIONS.CREATE_EVENTS,
    PERMISSIONS.EDIT_EVENTS,
    PERMISSIONS.DELETE_EVENTS,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.REGISTER_EVENTS,
    PERMISSIONS.MANAGE_EVENT_REGISTRATIONS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.EDIT_POSTS,
    PERMISSIONS.DELETE_POSTS,
    PERMISSIONS.MODERATE_COMMUNITY,
    PERMISSIONS.VIEW_COMMUNITY,
    PERMISSIONS.GENERATE_CERTIFICATES,
    PERMISSIONS.VIEW_CERTIFICATES,
    PERMISSIONS.MANAGE_CERTIFICATES,
    PERMISSIONS.UPLOAD_RESOURCES,
    PERMISSIONS.EDIT_RESOURCES,
    PERMISSIONS.DELETE_RESOURCES,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.APPROVE_RESOURCES,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.VIEW_DETAILED_ANALYTICS,
    PERMISSIONS.EXPORT_DATA,
    PERMISSIONS.MANAGE_PLATFORM,
    PERMISSIONS.MODERATE_CONTENT,
    PERMISSIONS.MANAGE_SETTINGS,
    PERMISSIONS.SEND_NOTIFICATIONS,
  ],

  student: [
    // Learning and participation focused
    PERMISSIONS.EDIT_OWN_PROFILE,
    PERMISSIONS.VIEW_COURSES,
    PERMISSIONS.ENROLL_COURSES,
    PERMISSIONS.VIEW_JOBS,
    PERMISSIONS.APPLY_JOBS,
    PERMISSIONS.BOOK_SESSIONS,
    PERMISSIONS.VIEW_MENTORSHIP,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.REGISTER_EVENTS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.VIEW_COMMUNITY,
    PERMISSIONS.GENERATE_CERTIFICATES,
    PERMISSIONS.VIEW_CERTIFICATES,
    PERMISSIONS.VIEW_RESOURCES,
  ],

  mentor: [
    // Mentorship and content creation focused
    PERMISSIONS.EDIT_OWN_PROFILE,
    PERMISSIONS.VIEW_COURSES,
    PERMISSIONS.ENROLL_COURSES,
    PERMISSIONS.CREATE_COURSES, // Can create courses
    PERMISSIONS.EDIT_COURSES, // Can edit own courses
    PERMISSIONS.VIEW_JOBS,
    PERMISSIONS.APPLY_JOBS,
    PERMISSIONS.CREATE_MENTOR_PROFILE,
    PERMISSIONS.MANAGE_SESSIONS,
    PERMISSIONS.VIEW_MENTORSHIP,
    PERMISSIONS.CREATE_EVENTS, // Can create events
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.REGISTER_EVENTS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.VIEW_COMMUNITY,
    PERMISSIONS.GENERATE_CERTIFICATES,
    PERMISSIONS.VIEW_CERTIFICATES,
    PERMISSIONS.UPLOAD_RESOURCES,
    PERMISSIONS.VIEW_RESOURCES,
  ],

  organisation: [
    // Combined employer and partner permissions - comprehensive organization access
    PERMISSIONS.EDIT_OWN_PROFILE,
    PERMISSIONS.VIEW_USER_PROFILES, // Can view candidate profiles
    PERMISSIONS.CREATE_COURSES, // Can create educational content
    PERMISSIONS.EDIT_COURSES, // Can edit own courses
    PERMISSIONS.VIEW_COURSES,
    PERMISSIONS.ENROLL_COURSES,
    PERMISSIONS.CREATE_JOBS, // Can post jobs
    PERMISSIONS.EDIT_JOBS, // Can edit own jobs
    PERMISSIONS.DELETE_JOBS, // Can delete own jobs
    PERMISSIONS.VIEW_JOBS,
    PERMISSIONS.MANAGE_APPLICATIONS, // Can manage job applications
    PERMISSIONS.VIEW_APPLICATIONS,
    PERMISSIONS.CREATE_MENTOR_PROFILE, // Can offer mentorship
    PERMISSIONS.VIEW_MENTORSHIP,
    PERMISSIONS.CREATE_EVENTS, // Can create events/job fairs
    PERMISSIONS.EDIT_EVENTS, // Can edit own events
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.REGISTER_EVENTS,
    PERMISSIONS.MANAGE_EVENT_REGISTRATIONS, // Can manage event registrations
    PERMISSIONS.CREATE_POSTS, // Can create community posts
    PERMISSIONS.MODERATE_COMMUNITY, // Can moderate community content
    PERMISSIONS.VIEW_COMMUNITY,
    PERMISSIONS.GENERATE_CERTIFICATES, // Can issue certificates
    PERMISSIONS.VIEW_CERTIFICATES,
    PERMISSIONS.UPLOAD_RESOURCES, // Can upload resources
    PERMISSIONS.EDIT_RESOURCES, // Can edit own resources
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.VIEW_ANALYTICS, // Can view analytics
    PERMISSIONS.SEND_NOTIFICATIONS, // Can send notifications
  ],
};

// Permission checking functions
export class PermissionChecker {
  static hasPermission(user: IUser, permission: string): boolean {
    if (!user || !user.role) return false;

    const rolePermissions = ROLE_PERMISSIONS[user.role as UserRole];
    return rolePermissions.includes(permission);
  }

  static hasAnyPermission(user: IUser, permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  static hasAllPermissions(user: IUser, permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  static canAccessResource(user: IUser, resource: string, action: string, resourceData?: any): boolean {
    const permission = `${resource}:${action}`;

    // Check basic permission
    if (!this.hasPermission(user, permission)) {
      return false;
    }

    // Additional checks for specific resources
    switch (resource) {
      case 'courses':
        if (action === 'edit' || action === 'delete') {
          // Only allow editing/deleting own courses (unless admin)
          if (user.role !== 'admin' && resourceData?.instructorId !== user._id.toString()) {
            return false;
          }
        }
        break;

      case 'jobs':
        if (action === 'edit' || action === 'delete' || action === 'manage_applications') {
          // Only allow managing own jobs (unless admin)
          if (user.role !== 'admin' && resourceData?.employerId !== user._id.toString()) {
            return false;
          }
        }
        break;

      case 'events':
        if (action === 'edit' || action === 'delete') {
          // Only allow editing/deleting own events (unless admin)
          if (user.role !== 'admin' && resourceData?.organizer?.userId !== user._id.toString()) {
            return false;
          }
        }
        break;

      case 'community':
        if (action === 'edit_posts' || action === 'delete_posts') {
          // Only allow editing/deleting own posts (unless admin/organisation with moderation rights)
          if (!['admin', 'organisation'].includes(user.role) && resourceData?.authorId !== user._id.toString()) {
            return false;
          }
        }
        break;

      case 'resources':
        if (action === 'edit' || action === 'delete') {
          // Only allow editing/deleting own resources (unless admin)
          if (user.role !== 'admin' && resourceData?.createdBy !== user._id.toString()) {
            return false;
          }
        }
        break;
    }

    return true;
  }

  static getPermissionsForRole(role: UserRole): string[] {
    return ROLE_PERMISSIONS[role] || [];
  }

  static canUpgradeToRole(currentRole: UserRole, targetRole: UserRole): boolean {
    // Define role hierarchy and upgrade rules
    const roleHierarchy = {
      student: 0,
      mentor: 1,
      organisation: 2,
      admin: 3,
    };

    // Students can become mentors or organisations
    if (currentRole === 'student' && ['mentor', 'organisation'].includes(targetRole)) {
      return true;
    }

    // Mentors can become organisations (with approval)
    if (currentRole === 'mentor' && targetRole === 'organisation') {
      return true;
    }

    // Only admins can create other admins
    if (targetRole === 'admin') {
      return false; // Requires manual admin intervention
    }

    return false;
  }
}

export default PermissionChecker;
