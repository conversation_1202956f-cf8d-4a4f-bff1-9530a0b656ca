import { SMSService } from './sms';
import { IUser } from '@/models/User';

export interface NotificationOptions {
  userId?: string;
  phone?: string;
  email?: string;
  title: string;
  message: string;
  type: 'welcome' | 'job_alert' | 'course_reminder' | 'mentorship_booking' | 'certification' | 'event_reminder' | 'system' | 'custom';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  data?: any;
}

export interface NotificationPreferences {
  sms: boolean;
  email: boolean;
  push: boolean;
}

export class NotificationService {
  /**
   * Send notification using the user's preferred method, with SMS as primary
   */
  static async sendNotification(options: NotificationOptions, user?: IUser) {
    const { title, message, type, priority, data, phone, email } = options;
    
    // Get user preferences (default to SMS-first)
    const preferences: NotificationPreferences = user?.preferences?.notifications || {
      sms: true,
      email: false,
      push: false
    };

    const results = {
      sms: null as any,
      email: null as any,
      push: null as any,
      success: false
    };

    // Always try SMS first if phone number is available and SMS is enabled
    const phoneNumber = phone || user?.phone;
    if (phoneNumber && preferences.sms) {
      try {
        results.sms = await this.sendSMSNotification(phoneNumber, title, message, type, data);
        results.success = true;
        console.log(`SMS notification sent successfully to ${phoneNumber}`);
      } catch (error) {
        console.error('Failed to send SMS notification:', error);
      }
    }

    // For urgent notifications, try multiple channels
    if (priority === 'urgent' || priority === 'high') {
      // Try email as backup if SMS failed and email is available
      const emailAddress = email || user?.email;
      if (emailAddress && preferences.email && !results.sms) {
        try {
          results.email = await this.sendEmailNotification(emailAddress, title, message, type, data);
          results.success = true;
          console.log(`Email notification sent successfully to ${emailAddress}`);
        } catch (error) {
          console.error('Failed to send email notification:', error);
        }
      }

      // Try push notification as final backup
      if (preferences.push && !results.sms && !results.email) {
        try {
          results.push = await this.sendPushNotification(user?.id || '', title, message, type, data);
          results.success = true;
          console.log(`Push notification sent successfully to user ${user?.id}`);
        } catch (error) {
          console.error('Failed to send push notification:', error);
        }
      }
    }

    return results;
  }

  /**
   * Send SMS notification using the SMS service
   */
  private static async sendSMSNotification(
    phoneNumber: string, 
    title: string, 
    message: string, 
    type: string, 
    data?: any
  ) {
    switch (type) {
      case 'welcome':
        return await SMSService.sendWelcomeSMS(phoneNumber, data?.name || 'User');
      
      case 'job_alert':
        return await SMSService.sendJobAlert(phoneNumber, data?.jobTitle || 'New Job', data?.company || 'Company');
      
      case 'course_reminder':
        return await SMSService.sendCourseReminder(phoneNumber, data?.courseName || 'Course');
      
      case 'mentorship_booking':
        return await SMSService.sendMentorshipBooking(phoneNumber, data?.mentorName || 'Mentor', data?.sessionTime || 'Soon');
      
      case 'certification':
        return await SMSService.sendCertificationAlert(phoneNumber, data?.courseName || 'Course');
      
      case 'event_reminder':
        return await SMSService.sendEventReminder(phoneNumber, data?.eventName || 'Event', data?.eventTime || 'Soon');
      
      case 'system':
        return await SMSService.sendSystemNotification(phoneNumber, title, message);
      
      default:
        return await SMSService.sendSMS({ to: phoneNumber, message: `${title}: ${message}` });
    }
  }

  /**
   * Send email notification (placeholder - implement with your email service)
   */
  private static async sendEmailNotification(
    email: string, 
    title: string, 
    message: string, 
    type: string, 
    data?: any
  ) {
    // TODO: Implement email service integration
    console.log(`Email notification would be sent to ${email}: ${title} - ${message}`);
    return { success: true, method: 'email' };
  }

  /**
   * Send push notification (placeholder - implement with your push service)
   */
  private static async sendPushNotification(
    userId: string, 
    title: string, 
    message: string, 
    type: string, 
    data?: any
  ) {
    // TODO: Implement push notification service integration
    console.log(`Push notification would be sent to user ${userId}: ${title} - ${message}`);
    return { success: true, method: 'push' };
  }

  /**
   * Send bulk notifications to multiple users
   */
  static async sendBulkNotification(
    users: IUser[], 
    options: Omit<NotificationOptions, 'userId' | 'phone' | 'email'>
  ) {
    const results = [];
    
    for (const user of users) {
      try {
        const result = await this.sendNotification({
          ...options,
          userId: user._id.toString(),
        }, user);
        
        results.push({
          userId: user._id.toString(),
          success: result.success,
          methods: Object.keys(result).filter(key => result[key] && key !== 'success')
        });
      } catch (error) {
        console.error(`Failed to send notification to user ${user._id}:`, error);
        results.push({
          userId: user._id.toString(),
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Send notification to all users with specific role
   */
  static async sendRoleBasedNotification(
    role: string, 
    options: Omit<NotificationOptions, 'userId' | 'phone' | 'email'>
  ) {
    // This would require importing User model, so we'll make it an API endpoint instead
    throw new Error('Use the API endpoint /api/notifications/role-based for role-based notifications');
  }

  /**
   * Send notification to organization members
   */
  static async sendOrganizationNotification(
    organizationId: string,
    options: Omit<NotificationOptions, 'userId' | 'phone' | 'email'>
  ) {
    // This would require importing User model, so we'll make it an API endpoint instead
    throw new Error('Use the API endpoint /api/notifications/organization for organization notifications');
  }
}

export default NotificationService;
