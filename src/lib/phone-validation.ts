/**
 * Phone number validation and formatting utilities for Kenyan phone numbers
 * Supports the format: +254 XXXXXXXXX (where X is 9 digits after country code)
 */

export interface PhoneValidationResult {
  isValid: boolean;
  formatted: string;
  error?: string;
}

export class PhoneValidator {
  // Kenyan country code
  private static readonly COUNTRY_CODE = '+254';
  
  // Valid Kenyan mobile network prefixes (after removing leading 0)
  private static readonly VALID_PREFIXES = [
    '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', // Safaricom
    '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', // Safaricom
    '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', // Airtel
    '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', // Airtel
    '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', // Airtel
    '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', // Telkom
    '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', // Telkom
  ];

  /**
   * Validates and formats a Kenyan phone number
   * @param phoneNumber - The phone number to validate
   * @returns PhoneValidationResult with validation status and formatted number
   */
  static validateAndFormat(phoneNumber: string): PhoneValidationResult {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number is required'
      };
    }

    // Remove all non-digit characters except +
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // Handle different input formats
    let digits = '';
    
    if (cleaned.startsWith('+254')) {
      // Already has country code: +254XXXXXXXXX
      digits = cleaned.substring(4);
    } else if (cleaned.startsWith('254')) {
      // Has country code without +: 254XXXXXXXXX
      digits = cleaned.substring(3);
    } else if (cleaned.startsWith('0')) {
      // Local format: 0XXXXXXXXX
      digits = cleaned.substring(1);
    } else if (cleaned.length === 9) {
      // Just the 9 digits: XXXXXXXXX
      digits = cleaned;
    } else {
      return {
        isValid: false,
        formatted: '',
        error: 'Invalid phone number format. Please use Kenyan format: +254 XXXXXXXXX'
      };
    }

    // Validate length (should be exactly 9 digits after country code)
    if (digits.length !== 9) {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number must have exactly 9 digits after the country code'
      };
    }

    // Validate that all characters are digits
    if (!/^\d{9}$/.test(digits)) {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number must contain only digits'
      };
    }

    // Validate network prefix
    const prefix = digits.substring(0, 2);
    if (!this.VALID_PREFIXES.includes(prefix)) {
      return {
        isValid: false,
        formatted: '',
        error: 'Invalid Kenyan mobile network prefix'
      };
    }

    // Format as +254 XXXXXXXXX
    const formatted = `${this.COUNTRY_CODE} ${digits}`;

    return {
      isValid: true,
      formatted,
    };
  }

  /**
   * Formats a phone number for display (adds spaces for readability)
   * @param phoneNumber - The phone number to format
   * @returns Formatted phone number or original if invalid
   */
  static formatForDisplay(phoneNumber: string): string {
    const result = this.validateAndFormat(phoneNumber);
    return result.isValid ? result.formatted : phoneNumber;
  }

  /**
   * Formats a phone number for storage (removes spaces)
   * @param phoneNumber - The phone number to format
   * @returns Formatted phone number for storage or null if invalid
   */
  static formatForStorage(phoneNumber: string): string | null {
    const result = this.validateAndFormat(phoneNumber);
    return result.isValid ? result.formatted.replace(/\s/g, '') : null;
  }

  /**
   * Checks if a phone number is valid
   * @param phoneNumber - The phone number to check
   * @returns True if valid, false otherwise
   */
  static isValid(phoneNumber: string): boolean {
    return this.validateAndFormat(phoneNumber).isValid;
  }

  /**
   * Gets validation error message for a phone number
   * @param phoneNumber - The phone number to validate
   * @returns Error message or null if valid
   */
  static getValidationError(phoneNumber: string): string | null {
    const result = this.validateAndFormat(phoneNumber);
    return result.error || null;
  }

  /**
   * Formats phone number as user types (for real-time formatting)
   * @param input - Current input value
   * @returns Formatted input value
   */
  static formatAsUserTypes(input: string): string {
    if (!input) return '';

    // Remove all non-digit characters except +
    const cleaned = input.replace(/[^\d+]/g, '');

    // If user starts typing without +254, add it
    if (cleaned && !cleaned.startsWith('+254') && !cleaned.startsWith('254') && !cleaned.startsWith('0')) {
      return `+254 ${cleaned}`;
    }

    // If user types +254 or 254, format it properly
    if (cleaned.startsWith('+254')) {
      const digits = cleaned.substring(4);
      if (digits.length === 0) return '+254 ';
      return `+254 ${digits}`;
    }

    if (cleaned.startsWith('254')) {
      const digits = cleaned.substring(3);
      if (digits.length === 0) return '+254 ';
      return `+254 ${digits}`;
    }

    // If user types 0XXXXXXXXX, convert to +254 format
    if (cleaned.startsWith('0')) {
      const digits = cleaned.substring(1);
      if (digits.length === 0) return '+254 ';
      return `+254 ${digits}`;
    }

    return input;
  }
}

// Export convenience functions
export const validatePhoneNumber = PhoneValidator.validateAndFormat;
export const formatPhoneNumber = PhoneValidator.formatForDisplay;
export const isValidPhoneNumber = PhoneValidator.isValid;
