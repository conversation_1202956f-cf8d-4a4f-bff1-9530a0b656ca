import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@next-auth/mongodb-adapter';
import bcrypt from 'bcryptjs';

// Lazy import MongoDB client only on server side
let clientPromise: Promise<any> | undefined;

if (typeof window === 'undefined') {
  // Only import and initialize on server side
  const { getClientPromise } = require('./mongodb');
  clientPromise = getClientPromise();
}

export const authOptions: NextAuthOptions = {
  adapter: clientPromise ? MongoDBAdapter(clientPromise) : undefined,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          if (!clientPromise) {
            throw new Error('MongoDB client not available');
          }
          const mongoClient = await clientPromise;
          const db = mongoClient.db();
          const user = await db.collection('users').findOne({
            email: credentials.email
          });

          if (!user) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            return null;
          }

          // Update last login
          await db.collection('users').updateOne(
            { _id: user._id },
            { $set: { lastLogin: new Date() } }
          );

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
            isActive: user.isActive,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/auth/signin',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = (user as any).role;
        token.avatar = (user as any).avatar;
        token.isActive = (user as any).isActive;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        (session.user as any).role = token.role as string;
        (session.user as any).avatar = token.avatar as string;
        (session.user as any).isActive = token.isActive as boolean;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Check if this is a sign-in redirect by looking at the URL
      const isSignInRedirect = url.includes('/auth/signin') || url.includes('callbackUrl');

      if (isSignInRedirect) {
        // For sign-in redirects, we'll handle role-based redirection in middleware
        return `${baseUrl}/dashboard`;
      }

      // Handle custom redirect URLs
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`;
      }

      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) {
        return url;
      }

      // Default redirect to home
      return baseUrl;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};
