import mongoose from 'mongoose';
import { MongoClient, Db } from 'mongodb';

// Only initialize on server side
function getMongoDBURI(): string {
  if (typeof window !== 'undefined') {
    throw new Error('MongoDB connection should only be used on the server side');
  }

  const MONGODB_URI = process.env.MONGODB_URI;
  if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
  }
  return MONGODB_URI;
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectDB() {
  // Ensure we're on server side
  if (typeof window !== 'undefined') {
    throw new Error('connectDB should only be called on the server side');
  }

  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      serverSelectionTimeoutMS: 30000, // 30 seconds
      socketTimeoutMS: 45000, // 45 seconds
      maxPoolSize: 10,
      minPoolSize: 5,
    };

    const MONGODB_URI = getMongoDBURI();
    cached.promise = mongoose.connect(MONGODB_URI, opts);
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

export default connectDB;
export { connectDB };

// For NextAuth MongoDB adapter - lazy initialization
export function getClientPromise(): Promise<MongoClient> {
  if (typeof window !== 'undefined') {
    throw new Error('MongoDB client should only be used on the server side');
  }

  if (!global._mongoClientPromise) {
    const MONGODB_URI = getMongoDBURI();
    const client = new MongoClient(MONGODB_URI);
    global._mongoClientPromise = client.connect();
  }
  return global._mongoClientPromise;
}

// For direct database operations
export async function connectToDatabase(): Promise<{ client: MongoClient; db: Db }> {
  if (typeof window !== 'undefined') {
    throw new Error('connectToDatabase should only be called on the server side');
  }

  const clientPromise = getClientPromise();
  const client = await clientPromise;
  const db = client.db();
  return { client, db };
}
