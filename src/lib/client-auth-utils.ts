import { Permission<PERSON>he<PERSON>, User<PERSON><PERSON> } from '@/lib/permissions';

/**
 * Permission checking utility for client-side
 * This file is safe to import in client components as it doesn't import any server-side dependencies
 */
export function createPermissionChecker(userRole: UserRole) {
  return {
    can: (permission: string) => {
      return PermissionChecker.hasPermission({ role: userRole } as any, permission);
    },
    
    canAny: (permissions: string[]) => {
      return PermissionChecker.hasAnyPermission({ role: userRole } as any, permissions);
    },
    
    canAll: (permissions: string[]) => {
      return PermissionChecker.hasAllPermissions({ role: userRole } as any, permissions);
    },
    
    is: (role: UserRole) => {
      return userRole === role;
    },
    
    isAny: (roles: UserRole[]) => {
      return roles.includes(userRole);
    }
  };
}

export default {
  createPermissionChecker,
};
