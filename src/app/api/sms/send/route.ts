import { NextRequest, NextResponse } from 'next/server';
import { SMSService } from '@/lib/sms';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { to, message, type, data } = body;

    if (!to || !message) {
      return NextResponse.json(
        { success: false, error: 'Phone number and message are required' },
        { status: 400 }
      );
    }

    let result;

    // Handle different SMS types with predefined templates
    switch (type) {
      case 'welcome':
        if (!data?.name) {
          return NextResponse.json(
            { success: false, error: 'Name is required for welcome SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendWelcomeSMS(to, data.name);
        break;

      case 'course_reminder':
        if (!data?.courseName) {
          return NextResponse.json(
            { success: false, error: 'Course name is required for course reminder SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendCourseReminder(to, data.courseName);
        break;

      case 'mentorship_booking':
        if (!data?.mentorName || !data?.sessionTime) {
          return NextResponse.json(
            { success: false, error: 'Mentor name and session time are required for mentorship booking SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendMentorshipBooking(to, data.mentorName, data.sessionTime);
        break;

      case 'job_alert':
        if (!data?.jobTitle || !data?.company) {
          return NextResponse.json(
            { success: false, error: 'Job title and company are required for job alert SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendJobAlert(to, data.jobTitle, data.company);
        break;

      case 'certification':
        if (!data?.courseName) {
          return NextResponse.json(
            { success: false, error: 'Course name is required for certification SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendCertificationAlert(to, data.courseName);
        break;

      case 'event_reminder':
        if (!data?.eventName || !data?.eventTime) {
          return NextResponse.json(
            { success: false, error: 'Event name and time are required for event reminder SMS' },
            { status: 400 }
          );
        }
        result = await SMSService.sendEventReminder(to, data.eventName, data.eventTime);
        break;

      case 'job_application_submitted':
        if (!data?.applicantName || !data?.jobTitle || !data?.companyName) {
          return NextResponse.json(
            { success: false, error: 'Applicant name, job title, and company name are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendJobApplicationSubmitted(to, data.applicantName, data.jobTitle, data.companyName);
        break;

      case 'new_job_application':
        if (!data?.employerName || !data?.applicantName || !data?.jobTitle) {
          return NextResponse.json(
            { success: false, error: 'Employer name, applicant name, and job title are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendNewJobApplication(to, data.employerName, data.applicantName, data.jobTitle);
        break;

      case 'application_status_update':
        if (!data?.applicantName || !data?.jobTitle || !data?.status) {
          return NextResponse.json(
            { success: false, error: 'Applicant name, job title, and status are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendApplicationStatusUpdate(to, data.applicantName, data.jobTitle, data.status);
        break;

      case 'session_reminder':
        if (!data?.sessionTitle || !data?.sessionTime || !data?.mentorName) {
          return NextResponse.json(
            { success: false, error: 'Session title, time, and mentor name are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendSessionReminder(to, data.sessionTitle, data.sessionTime, data.mentorName);
        break;

      case 'event_registration':
        if (!data?.userName || !data?.eventTitle || !data?.eventDate) {
          return NextResponse.json(
            { success: false, error: 'User name, event title, and event date are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendEventRegistrationConfirmation(to, data.userName, data.eventTitle, data.eventDate);
        break;

      case 'course_enrollment':
        if (!data?.userName || !data?.courseName) {
          return NextResponse.json(
            { success: false, error: 'User name and course name are required' },
            { status: 400 }
          );
        }
        result = await SMSService.sendCourseEnrollmentConfirmation(to, data.userName, data.courseName);
        break;

      case 'system_notification':
        if (!data?.title || !message) {
          return NextResponse.json(
            { success: false, error: 'Title and message are required for system notifications' },
            { status: 400 }
          );
        }
        result = await SMSService.sendSystemNotification(to, data.title, message);
        break;

      case 'custom':
        result = await SMSService.sendSMS({ to, message });
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid SMS type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: 'SMS sent successfully',
    });
  } catch (error) {
    console.error('Error sending SMS:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to send SMS' },
      { status: 500 }
    );
  }
}
