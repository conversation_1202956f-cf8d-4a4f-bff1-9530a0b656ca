import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { ForumPost, UserReputation } from '@/models/Community';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'recent';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = { status: 'active' };

    if (category && category !== 'All Categories') {
      query.category = category;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Build sort criteria
    let sortCriteria: any = {};
    switch (sortBy) {
      case 'popular':
        sortCriteria = { likes: -1, views: -1 };
        break;
      case 'replies':
        sortCriteria = { replies: -1 };
        break;
      case 'views':
        sortCriteria = { views: -1 };
        break;
      default: // recent
        sortCriteria = { isPinned: -1, lastReplyAt: -1, createdAt: -1 };
    }

    const posts = await ForumPost.find(query)
      .populate('authorId', 'name avatar profile.bio role')
      .populate('lastReplyBy', 'name avatar')
      .sort(sortCriteria)
      .skip(skip)
      .limit(limit);

    const total = await ForumPost.countDocuments(query);

    // Get categories for filtering
    const categories = await ForumPost.distinct('category', { status: 'active' });

    return NextResponse.json({
      success: true,
      data: {
        posts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        filters: {
          categories: ['All Categories', ...categories],
        },
      },
    });
  } catch (error) {
    console.error('Error fetching forum posts:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const { title, content, category, tags } = body;

    // Validate required fields
    if (!title || !content || !category) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate content length
    if (title.length > 200) {
      return NextResponse.json(
        { success: false, error: 'Title too long (max 200 characters)' },
        { status: 400 }
      );
    }

    if (content.length > 10000) {
      return NextResponse.json(
        { success: false, error: 'Content too long (max 10000 characters)' },
        { status: 400 }
      );
    }

    // Create post
    const post = new ForumPost({
      title,
      content,
      authorId: session.user.id,
      category,
      tags: tags || [],
    });

    await post.save();

    // Update user reputation
    try {
      let userReputation = await UserReputation.findOne({ userId: session.user.id });
      if (!userReputation) {
        userReputation = new UserReputation({
          userId: session.user.id,
          totalPoints: 0,
          level: 'Newcomer',
        });
      }

      // Add points for creating a post
      const pointsEarned = 5;
      userReputation.totalPoints += pointsEarned;
      userReputation.activities.push({
        type: 'post_created',
        points: pointsEarned,
        description: `Created post: ${title}`,
        createdAt: new Date(),
      });

      // Update level based on points
      if (userReputation.totalPoints >= 1000) {
        userReputation.level = 'Expert';
      } else if (userReputation.totalPoints >= 500) {
        userReputation.level = 'Advanced';
      } else if (userReputation.totalPoints >= 100) {
        userReputation.level = 'Intermediate';
      } else if (userReputation.totalPoints >= 25) {
        userReputation.level = 'Contributor';
      }

      await userReputation.save();
    } catch (reputationError) {
      console.error('Error updating user reputation:', reputationError);
    }

    // Populate the post with author details
    await post.populate('authorId', 'name avatar profile.bio role');

    return NextResponse.json({
      success: true,
      data: post,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating forum post:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create post' },
      { status: 500 }
    );
  }
}
