import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { SkillAssessment } from '@/models/Certificate';
import { GeminiService } from '@/lib/gemini';

// Sample questions for different skill areas
const ASSESSMENT_QUESTIONS = {
  'Satellite Engineering': [
    {
      question: 'What is the primary function of a satellite\'s attitude control system?',
      type: 'multiple_choice',
      options: [
        'To control the satellite\'s orbital velocity',
        'To maintain the satellite\'s orientation in space',
        'To manage power distribution',
        'To handle communication protocols'
      ],
      correctAnswer: 'To maintain the satellite\'s orientation in space',
      points: 10,
      difficulty: 'medium'
    },
    {
      question: 'Which orbit is most commonly used for geostationary satellites?',
      type: 'multiple_choice',
      options: [
        'Low Earth Orbit (LEO)',
        'Medium Earth Orbit (MEO)',
        'Geostationary Earth Orbit (GEO)',
        'Highly Elliptical Orbit (HEO)'
      ],
      correctAnswer: 'Geostationary Earth Orbit (GEO)',
      points: 10,
      difficulty: 'easy'
    },
    {
      question: 'Explain the concept of orbital mechanics and how it applies to satellite deployment.',
      type: 'essay',
      correctAnswer: 'Orbital mechanics involves the study of motion of satellites under gravitational forces...',
      points: 20,
      difficulty: 'hard'
    }
  ],
  'AI in Space': [
    {
      question: 'What is the main advantage of using machine learning for satellite imagery analysis?',
      type: 'multiple_choice',
      options: [
        'Reduced computational requirements',
        'Automated pattern recognition and classification',
        'Lower data storage needs',
        'Simplified hardware requirements'
      ],
      correctAnswer: 'Automated pattern recognition and classification',
      points: 10,
      difficulty: 'medium'
    },
    {
      question: 'Which AI technique is most suitable for autonomous spacecraft navigation?',
      type: 'multiple_choice',
      options: [
        'Natural Language Processing',
        'Computer Vision and Reinforcement Learning',
        'Speech Recognition',
        'Text Classification'
      ],
      correctAnswer: 'Computer Vision and Reinforcement Learning',
      points: 15,
      difficulty: 'hard'
    }
  ],
  'Cybersecurity': [
    {
      question: 'What is the primary security concern for satellite communication systems?',
      type: 'multiple_choice',
      options: [
        'Physical theft of satellites',
        'Signal interception and jamming',
        'Power consumption',
        'Orbital debris'
      ],
      correctAnswer: 'Signal interception and jamming',
      points: 10,
      difficulty: 'medium'
    },
    {
      question: 'Which encryption method is most suitable for space-based communications?',
      type: 'multiple_choice',
      options: [
        'Symmetric encryption only',
        'Asymmetric encryption only',
        'Hybrid encryption combining both symmetric and asymmetric',
        'No encryption needed'
      ],
      correctAnswer: 'Hybrid encryption combining both symmetric and asymmetric',
      points: 15,
      difficulty: 'hard'
    }
  ]
};

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const skillArea = searchParams.get('skillArea');
    const status = searchParams.get('status');

    const query: any = { userId: session.user.id };
    
    if (skillArea) {
      query.skillArea = skillArea;
    }
    
    if (status) {
      query.status = status;
    }

    const assessments = await SkillAssessment.find(query)
      .sort({ createdAt: -1 });

    return NextResponse.json({
      success: true,
      data: assessments,
    });
  } catch (error) {
    console.error('Error fetching assessments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch assessments' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const { skillArea, assessmentType = 'quiz' } = body;

    // Validate skill area
    if (!skillArea || !ASSESSMENT_QUESTIONS[skillArea as keyof typeof ASSESSMENT_QUESTIONS]) {
      return NextResponse.json(
        { success: false, error: 'Invalid or unsupported skill area' },
        { status: 400 }
      );
    }

    // Check if user has an active assessment for this skill area
    const activeAssessment = await SkillAssessment.findOne({
      userId: session.user.id,
      skillArea,
      status: 'in_progress',
    });

    if (activeAssessment) {
      return NextResponse.json(
        { success: false, error: 'You already have an active assessment for this skill area' },
        { status: 409 }
      );
    }

    // Get questions for the skill area
    const questions = ASSESSMENT_QUESTIONS[skillArea as keyof typeof ASSESSMENT_QUESTIONS];

    // Create assessment
    const assessment = new SkillAssessment({
      userId: session.user.id,
      skillArea,
      assessmentType,
      questions: questions.map(q => ({
        ...q,
        userAnswer: undefined, // Will be filled when user submits answers
      })),
      status: 'in_progress',
      expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    });

    await assessment.save();

    // Return assessment without correct answers
    const assessmentForUser = {
      ...assessment.toObject(),
      questions: assessment.questions.map(q => ({
        question: q.question,
        type: q.type,
        options: q.options,
        points: q.points,
        difficulty: q.difficulty,
        // Don't include correctAnswer
      })),
    };

    return NextResponse.json({
      success: true,
      data: assessmentForUser,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating assessment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create assessment' },
      { status: 500 }
    );
  }
}
