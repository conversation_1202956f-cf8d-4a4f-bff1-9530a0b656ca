import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Resource } from '@/models/Resource';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const level = searchParams.get('level');
    const sortBy = searchParams.get('sortBy') || 'recent';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = { 
      status: 'published',
      'access.visibility': { $in: ['public', 'members_only'] }
    };

    if (type) {
      query.type = type;
    }

    if (category) {
      query.category = category;
    }

    if (level && level !== 'all') {
      query['metadata.level'] = level;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
        { 'metadata.subject': { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort criteria
    let sortCriteria: any = {};
    switch (sortBy) {
      case 'popular':
        sortCriteria = { 'analytics.popularityScore': -1, 'engagement.views': -1 };
        break;
      case 'rating':
        sortCriteria = { 'engagement.averageRating': -1, 'engagement.totalRatings': -1 };
        break;
      case 'downloads':
        sortCriteria = { 'engagement.downloads': -1 };
        break;
      default: // recent
        sortCriteria = { createdAt: -1 };
    }

    const resources = await Resource.find(query)
      .populate('createdBy', 'name avatar')
      .populate('author.userId', 'name avatar')
      .sort(sortCriteria)
      .skip(skip)
      .limit(limit);

    const total = await Resource.countDocuments(query);

    // Get filter options
    const types = await Resource.distinct('type', { status: 'published' });
    const categories = await Resource.distinct('category', { status: 'published' });
    const levels = await Resource.distinct('metadata.level', { status: 'published' });

    return NextResponse.json({
      success: true,
      data: {
        resources,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        filters: {
          types,
          categories,
          levels,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching resources:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch resources' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const {
      title,
      description,
      type,
      category,
      subcategory,
      tags,
      author,
      file,
      metadata,
      access,
      prerequisites,
      learningOutcomes,
    } = body;

    // Validate required fields
    if (!title || !description || !type || !category || !file) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate file information
    if (!file.originalName || !file.url || !file.size || !file.mimeType) {
      return NextResponse.json(
        { success: false, error: 'Invalid file information' },
        { status: 400 }
      );
    }

    // Create resource
    const resource = new Resource({
      title,
      description,
      type,
      category,
      subcategory,
      tags: tags || [],
      author: {
        name: author?.name || session.user.name,
        organization: author?.organization,
        email: author?.email || session.user.email,
        userId: session.user.id,
      },
      file: {
        ...file,
        uploadedAt: new Date(),
      },
      metadata: {
        language: metadata?.language || 'en',
        level: metadata?.level || 'all',
        subject: metadata?.subject || category,
        duration: metadata?.duration,
        pages: metadata?.pages,
        resolution: metadata?.resolution,
      },
      access: {
        visibility: access?.visibility || 'public',
        allowedRoles: access?.allowedRoles || ['student', 'mentor', 'employer'],
        price: access?.price || 0,
        currency: access?.currency || 'USD',
        isFree: access?.price ? false : true,
      },
      prerequisites: prerequisites || [],
      learningOutcomes: learningOutcomes || [],
      status: 'under_review', // Resources need approval
      createdBy: session.user.id,
    });

    await resource.save();

    return NextResponse.json({
      success: true,
      data: resource,
      message: 'Resource uploaded successfully. Awaiting approval.',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating resource:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create resource' },
      { status: 500 }
    );
  }
}
