import { NextRequest, NextResponse } from 'next/server';
import { GeminiService } from '@/lib/gemini';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Course from '@/models/Course';
import Job from '@/models/Job';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { type, userId, userProfile, additionalData } = body;

    if (!type) {
      return NextResponse.json(
        { success: false, error: 'Recommendation type is required' },
        { status: 400 }
      );
    }

    let result;

    switch (type) {
      case 'learning_path':
        if (!userProfile) {
          return NextResponse.json(
            { success: false, error: 'User profile is required for learning path recommendations' },
            { status: 400 }
          );
        }
        result = await GeminiService.generatePersonalizedLearningPath(userProfile);
        break;

      case 'mentor_matching':
        if (!userProfile || !additionalData?.availableMentors) {
          return NextResponse.json(
            { success: false, error: 'User profile and available mentors are required for mentor matching' },
            { status: 400 }
          );
        }
        result = await GeminiService.matchMentorToMentee(userProfile, additionalData.availableMentors);
        break;

      case 'job_recommendations':
        if (!userProfile) {
          return NextResponse.json(
            { success: false, error: 'User profile is required for job recommendations' },
            { status: 400 }
          );
        }

        // Fetch available jobs from database
        const availableJobs = await Job.find({ isActive: true })
          .select('title company requirements type location')
          .limit(20)
          .lean();

        result = await GeminiService.generateJobRecommendations(userProfile, availableJobs);
        break;

      case 'skill_assessment':
        if (!additionalData?.userAnswers || !additionalData?.skillArea) {
          return NextResponse.json(
            { success: false, error: 'User answers and skill area are required for skill assessment' },
            { status: 400 }
          );
        }
        result = await GeminiService.generateSkillAssessment(
          additionalData.userAnswers,
          additionalData.skillArea
        );
        break;

      case 'course_recommendations':
        if (!userProfile) {
          return NextResponse.json(
            { success: false, error: 'User profile is required for course recommendations' },
            { status: 400 }
          );
        }

        // Fetch available courses from database
        const availableCourses = await Course.find({ isPublished: true })
          .select('title description category level prerequisites tags')
          .limit(30)
          .lean();

        // Generate AI recommendations based on user profile and available courses
        const courseRecommendationPrompt = `
        Based on the following user profile, recommend the most suitable courses:
        
        User Profile:
        - Skills: ${userProfile.skills?.join(', ') || 'Not specified'}
        - Interests: ${userProfile.interests?.join(', ') || 'Not specified'}
        - Current Level: ${userProfile.currentLevel || 'Beginner'}
        - Career Goals: ${userProfile.careerGoals?.join(', ') || 'Not specified'}
        
        Available Courses:
        ${availableCourses.map((course, index) => `
        ${index + 1}. ${course.title}
           - Category: ${course.category}
           - Level: ${course.level}
           - Description: ${course.description}
           - Prerequisites: ${course.prerequisites?.join(', ') || 'None'}
        `).join('')}
        
        Provide the top 5 course recommendations with explanations for each recommendation.
        `;

        result = await GeminiService.model.generateContent(courseRecommendationPrompt);
        const response = await result.response;
        result = response.text();
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid recommendation type' },
          { status: 400 }
        );
    }

    // If userId is provided, we could save the recommendation to the user's profile
    if (userId) {
      try {
        await User.findByIdAndUpdate(userId, {
          $push: {
            'aiRecommendations': {
              type,
              recommendation: result,
              createdAt: new Date(),
            }
          }
        });
      } catch (error) {
        console.error('Error saving recommendation to user profile:', error);
        // Don't fail the request if saving fails
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        type,
        recommendation: result,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate recommendations' },
      { status: 500 }
    );
  }
}
