import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Job } from '@/models/Job';
import { JobApplication } from '@/models/JobApplication';
import { Course } from '@/models/Course';
import { Enrollment } from '@/models/Enrollment';
import { Event } from '@/models/Event';
import { MentorshipSession } from '@/models/Mentorship';
import { Certificate } from '@/models/Certificate';
import { ForumPost } from '@/models/Community';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    // Check if user is an organisation
    if (req.user?.role !== 'organisation' && req.user?.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Organisation role required.' },
        { status: 403 }
      );
    }
    try {
      await connectDB();

      const userId = req.user?.id;

      // Get comprehensive organisation statistics
      const [
        activeJobs,
        totalApplications,
        newApplications,
        interviewsScheduled,
        hiredCandidates,
        activeCourses,
        totalStudents,
        upcomingEvents,
        communityPosts,
        certificatesIssued,
        mentorshipSessions,
      ] = await Promise.all([
        // Recruitment Stats
        Job.countDocuments({ employerId: userId, isActive: true }),
        JobApplication.countDocuments({ employerId: userId }),
        JobApplication.countDocuments({
          employerId: userId,
          status: 'applied',
          appliedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }),
        JobApplication.countDocuments({
          employerId: userId,
          status: 'interview_scheduled'
        }),
        JobApplication.countDocuments({
          employerId: userId,
          status: 'hired'
        }),

        // Education & Training Stats
        Course.countDocuments({ instructorId: userId, isActive: true }),
        Enrollment.countDocuments({
          courseId: { $in: await Course.find({ instructorId: userId }).distinct('_id') }
        }),

        // Events & Community Stats
        Event.countDocuments({
          'organizer.userId': userId,
          'schedule.startDate': { $gte: new Date() },
          status: 'published'
        }),
        ForumPost.countDocuments({ authorId: userId }),
        Certificate.countDocuments({
          courseId: { $in: await Course.find({ instructorId: userId }).distinct('_id') }
        }),
        MentorshipSession.countDocuments({ mentorId: userId }),
      ]);

      // Profile views - placeholder for now (would need analytics tracking)
      const profileViews = 0;

      const stats = {
        activeJobs,
        totalApplications,
        newApplications,
        interviewsScheduled,
        hiredCandidates,
        profileViews,
        activeCourses,
        totalStudents,
        upcomingEvents,
        communityPosts,
        certificatesIssued,
        mentorshipSessions,
      };

      return NextResponse.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error fetching organisation stats:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch organisation statistics' },
        { status: 500 }
      );
    }
  });
}
