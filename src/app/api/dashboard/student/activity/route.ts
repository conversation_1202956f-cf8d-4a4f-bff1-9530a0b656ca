import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Enrollment } from '@/models/Enrollment';
import { Certificate } from '@/models/Certificate';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const limit = parseInt(searchParams.get('limit') || '10');

      // Get recent activities
      const [recentEnrollments, recentCertificates] = await Promise.all([
        Enrollment.find({ userId })
          .populate('courseId', 'title')
          .sort({ enrolledAt: -1 })
          .limit(5),
        Certificate.find({ userId, status: 'issued' })
          .sort({ issuedAt: -1 })
          .limit(5),
      ]);

      // Combine and format activities
      const activities = [];

      // Add enrollment activities
      recentEnrollments.forEach(enrollment => {
        const course = enrollment.courseId as any;
        activities.push({
          id: `enrollment-${enrollment._id}`,
          type: 'course_enrolled',
          title: 'Course Enrolled',
          description: `Enrolled in ${course.title}`,
          date: enrollment.enrolledAt,
          link: `/courses/${course._id}`,
        });

        if (enrollment.isCompleted && enrollment.completedAt) {
          activities.push({
            id: `completion-${enrollment._id}`,
            type: 'course_completed',
            title: 'Course Completed',
            description: `Completed ${course.title}`,
            date: enrollment.completedAt,
            link: `/courses/${course._id}`,
          });
        }
      });

      // Add certificate activities
      recentCertificates.forEach(certificate => {
        activities.push({
          id: `certificate-${certificate._id}`,
          type: 'certificate_earned',
          title: 'Certificate Earned',
          description: certificate.title,
          date: certificate.issuedAt,
          link: `/certificates/${certificate._id}`,
        });
      });

      // Sort by date and limit
      const sortedActivities = activities
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);

      return NextResponse.json({
        success: true,
        data: sortedActivities,
      });
    } catch (error) {
      console.error('Error fetching student activity:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch recent activity' },
        { status: 500 }
      );
    }
  });
}
