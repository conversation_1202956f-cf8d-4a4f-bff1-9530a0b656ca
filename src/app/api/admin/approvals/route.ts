import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Course } from '@/models/Course';
import { Resource } from '@/models/Resource';
import { Event } from '@/models/Event';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get pending approvals
    const [
      pendingMentors,
      pendingEmployers,
      pendingPartners,
      unpublishedCourses,
      pendingResources,
      draftEvents
    ] = await Promise.all([
      // Pending mentor applications
      User.find({
        'mentorship.isMentor': true,
        'mentorship.isApproved': false
      })
      .select('name email mentorship.appliedAt mentorship.expertise')
      .sort({ 'mentorship.appliedAt': -1 })
      .limit(limit),

      // Pending employer verifications
      User.find({
        'employment.isEmployer': true,
        'employment.companyVerified': false
      })
      .select('name email employment.company employment.companyInfo')
      .sort({ createdAt: -1 })
      .limit(limit),

      // Pending partner applications
      User.find({
        'partnership.isPartner': true,
        'partnership.isApproved': false
      })
      .select('name email partnership.appliedAt partnership.organizationInfo')
      .sort({ 'partnership.appliedAt': -1 })
      .limit(limit),

      // Unpublished courses
      Course.find({ isPublished: false })
        .select('title instructor category createdAt')
        .populate('instructorId', 'name email')
        .sort({ createdAt: -1 })
        .limit(limit),

      // Pending resources
      Resource.find({ status: 'under_review' })
        .select('title type category author.name createdAt')
        .sort({ createdAt: -1 })
        .limit(limit),

      // Draft events
      Event.find({ status: 'draft' })
        .select('title type organizer.name schedule.startDate createdAt')
        .sort({ createdAt: -1 })
        .limit(limit)
    ]);

    const approvals = {
      mentors: pendingMentors.map(user => ({
        id: user._id,
        type: 'mentor',
        name: user.name,
        email: user.email,
        appliedAt: user.mentorship?.appliedAt,
        expertise: user.mentorship?.expertise,
        status: 'pending'
      })),
      employers: pendingEmployers.map(user => ({
        id: user._id,
        type: 'employer',
        name: user.name,
        email: user.email,
        company: user.employment?.company,
        companyInfo: user.employment?.companyInfo,
        status: 'pending'
      })),
      partners: pendingPartners.map(user => ({
        id: user._id,
        type: 'partner',
        name: user.name,
        email: user.email,
        appliedAt: user.partnership?.appliedAt,
        organizationInfo: user.partnership?.organizationInfo,
        status: 'pending'
      })),
      courses: unpublishedCourses.map(course => ({
        id: course._id,
        type: 'course',
        title: course.title,
        instructor: course.instructor,
        category: course.category,
        createdAt: course.createdAt,
        status: 'pending'
      })),
      resources: pendingResources.map(resource => ({
        id: resource._id,
        type: 'resource',
        title: resource.title,
        resourceType: resource.type,
        category: resource.category,
        author: resource.author?.name,
        createdAt: resource.createdAt,
        status: 'pending'
      })),
      events: draftEvents.map(event => ({
        id: event._id,
        type: 'event',
        title: event.title,
        eventType: event.type,
        organizer: event.organizer?.name,
        startDate: event.schedule?.startDate,
        createdAt: event.createdAt,
        status: 'pending'
      }))
    };

    // Flatten all approvals and sort by creation date
    const allApprovals = [
      ...approvals.mentors,
      ...approvals.employers,
      ...approvals.partners,
      ...approvals.courses,
      ...approvals.resources,
      ...approvals.events
    ].sort((a, b) => {
      const dateA = new Date(a.appliedAt || a.createdAt);
      const dateB = new Date(b.appliedAt || b.createdAt);
      return dateB.getTime() - dateA.getTime();
    }).slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        approvals: allApprovals,
        summary: {
          total: allApprovals.length,
          mentors: approvals.mentors.length,
          employers: approvals.employers.length,
          partners: approvals.partners.length,
          courses: approvals.courses.length,
          resources: approvals.resources.length,
          events: approvals.events.length
        }
      }
    });
  } catch (error) {
    console.error('Error fetching approvals:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch approvals' },
      { status: 500 }
    );
  }
}
