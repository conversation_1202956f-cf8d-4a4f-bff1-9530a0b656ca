'use client';

import React, { useState, useEffect } from 'react';
import { OrganisationLayout } from '@/components/layout/OrganisationLayout';

interface OrganisationDashboardLayoutProps {
  children: React.ReactNode;
}

export default function OrganisationDashboardLayout({ children }: OrganisationDashboardLayoutProps) {
  const [newApplications, setNewApplications] = useState(0);
  const [activeJobs, setActiveJobs] = useState(0);
  const [activeCourses, setActiveCourses] = useState(0);
  const [upcomingEvents, setUpcomingEvents] = useState(0);

  useEffect(() => {
    // Fetch notification counts for the navbar
    const fetchNotificationCounts = async () => {
      try {
        const [statsRes] = await Promise.all([
          fetch('/api/dashboard/organisation/stats'),
        ]);

        if (statsRes.ok) {
          const statsData = await statsRes.json();
          setNewApplications(statsData.data?.newApplications || 0);
          setActiveJobs(statsData.data?.activeJobs || 0);
          setActiveCourses(statsData.data?.activeCourses || 0);
          setUpcomingEvents(statsData.data?.upcomingEvents || 0);
        }
      } catch (error) {
        console.error('Failed to fetch notification counts:', error);
      }
    };

    fetchNotificationCounts();

    // Refresh counts every 30 seconds
    const interval = setInterval(fetchNotificationCounts, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <OrganisationLayout
      newApplications={newApplications}
      activeJobs={activeJobs}
      activeCourses={activeCourses}
      upcomingEvents={upcomingEvents}
    >
      {children}
    </OrganisationLayout>
  );
}
