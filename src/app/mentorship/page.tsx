'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { StudentLayout } from '@/components/layout/StudentLayout';
import { OrganisationLayout } from '@/components/layout/OrganisationLayout';
import { AdminLayout } from '@/components/layout/AdminLayout';
import {
  Users,
  Star,
  MapPin,
  Clock,
  Video,
  MessageSquare,
  Award,
  Search,
  Filter,
  Calendar,
  ArrowRight,
  UserCheck
} from 'lucide-react';

// Mock data for mentors
const mockMentors = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    title: 'Senior Satellite Engineer',
    company: 'Kenya Space Agency',
    expertise: ['Satellite Engineering', 'Orbital Mechanics', 'Space Systems'],
    rating: 4.9,
    sessions: 127,
    location: 'Nairobi, Kenya',
    avatar: '/api/placeholder/80/80',
    price: 0,
    isFree: true,
    availability: 'Available this week',
    bio: 'Leading satellite engineer with 10+ years experience in space systems design and orbital mechanics.',
  },
  {
    id: 2,
    name: 'Prof. <PERSON><PERSON>',
    title: 'AI Research Director',
    company: 'Ghana Space Science Institute',
    expertise: ['AI in Space', 'Machine Learning', 'Data Analysis'],
    rating: 4.8,
    sessions: 89,
    location: 'Accra, Ghana',
    avatar: '/api/placeholder/80/80',
    price: 50,
    isFree: false,
    availability: 'Available next week',
    bio: 'AI researcher specializing in machine learning applications for space exploration and satellite data analysis.',
  },
  {
    id: 3,
    name: 'Dr. Fatima Al-Rashid',
    title: 'Cybersecurity Specialist',
    company: 'Egyptian Space Agency',
    expertise: ['Cybersecurity', 'Space Systems Security', 'Risk Assessment'],
    rating: 4.9,
    sessions: 156,
    location: 'Cairo, Egypt',
    avatar: '/api/placeholder/80/80',
    price: 75,
    isFree: false,
    availability: 'Available today',
    bio: 'Cybersecurity expert focused on protecting space infrastructure and satellite communication systems.',
  },
];

const expertiseAreas = [
  'All Areas',
  'Satellite Engineering',
  'AI in Space',
  'Cybersecurity',
  'Data Science',
  'Robotics',
  'Astrophysics',
  'Space Law',
  'Business & Entrepreneurship'
];

export default function MentorshipPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedExpertise, setSelectedExpertise] = useState('All Areas');
  const [showFreeOnly, setShowFreeOnly] = useState(false);

  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const dashboardContext = searchParams.get('from');
  const userRole = (session?.user as any)?.role;

  // Determine if this page is being accessed from a dashboard
  const isDashboardContext = dashboardContext && ['student', 'organisation', 'admin', 'mentor'].includes(dashboardContext);

  // Mentorship content component
  const MentorshipContent = () => {
    if (isDashboardContext) {
      // Dashboard context - no header/footer, just content
      return (
        <>
          {/* Hero Section */}
          <section className="relative pt-8 pb-20 overflow-hidden parallax-container z-10">
            {/* Cosmic Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
              <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
            </div>

            <div className="relative section-container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center"
              >
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <UserCheck className="h-16 w-16 galaxy-text-blue" />
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
                  </div>
                </div>
                <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
                  Find Your Space Tech Mentor
                </h1>
                <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                  Connect with industry experts and experienced professionals for personalized guidance
                  and accelerate your career in space technology.
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-gray-300">
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <Users className="h-5 w-5 galaxy-text-blue" />
                    <span>150+ Expert Mentors</span>
                  </div>
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <Video className="h-5 w-5 galaxy-text-purple" />
                    <span>1-on-1 Sessions</span>
                  </div>
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <Award className="h-5 w-5 galaxy-text-pink" />
                    <span>Industry Certified</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>
          {renderMentorshipContent()}
        </>
      );
    }

    // Standalone page - with header/footer
    return (
      <div className="min-h-screen galaxy-background">
        {/* Star Field Background */}
        <div className="star-field"></div>

        {/* Muted Galaxy Background Overlay */}
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

        <Header />

        {/* Hero Section */}
        <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
          {/* Cosmic Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
            <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
          </div>

          <div className="relative section-container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <UserCheck className="h-16 w-16 galaxy-text-blue" />
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
                Find Your Space Tech Mentor
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Connect with industry experts and experienced professionals for personalized guidance
                and accelerate your career in space technology.
              </p>
              <div className="flex flex-wrap justify-center gap-6 text-gray-300">
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <Users className="h-5 w-5 galaxy-text-blue" />
                  <span>150+ Expert Mentors</span>
                </div>
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <Video className="h-5 w-5 galaxy-text-purple" />
                  <span>1-on-1 Sessions</span>
                </div>
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <Award className="h-5 w-5 galaxy-text-pink" />
                  <span>Industry Certified</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {renderMentorshipContent()}

        <Footer />
      </div>
    );
  };

  // Shared mentorship content (filters, mentors list, etc.)
  const renderMentorshipContent = () => (
    <>

      {/* Filters Section */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search mentors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            {/* Expertise Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={selectedExpertise}
                onChange={(e) => setSelectedExpertise(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                {expertiseAreas.map(area => (
                  <option key={area} value={area}>{area}</option>
                ))}
              </select>
            </div>

            {/* Free Only Filter */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showFreeOnly}
                onChange={(e) => setShowFreeOnly(e.target.checked)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <span className="text-gray-700">Free sessions only</span>
            </label>
          </div>
        </div>
      </section>

      {/* Mentors Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockMentors.map((mentor, index) => (
              <motion.div
                key={mentor.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              >
                {/* Mentor Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      {mentor.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {mentor.name}
                      </h3>
                      <p className="text-purple-600 font-medium mb-1">
                        {mentor.title}
                      </p>
                      <p className="text-gray-600 text-sm">
                        {mentor.company}
                      </p>
                    </div>
                    {mentor.isFree && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        Free
                      </span>
                    )}
                  </div>
                </div>

                {/* Mentor Details */}
                <div className="p-6">
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {mentor.bio}
                  </p>

                  {/* Expertise Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {mentor.expertise.slice(0, 3).map((skill, idx) => (
                      <span
                        key={idx}
                        className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span>{mentor.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{mentor.sessions} sessions</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-4 w-4" />
                      <span>{mentor.location.split(',')[0]}</span>
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="flex items-center space-x-2 mb-4">
                    <Clock className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-600">{mentor.availability}</span>
                  </div>

                  {/* Price and CTA */}
                  <div className="flex items-center justify-between">
                    <div className="text-lg font-bold text-gray-900">
                      {mentor.isFree ? 'Free' : `$${mentor.price}/session`}
                    </div>
                    <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Book Session</span>
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              How Mentorship Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get personalized guidance from industry experts in just a few simple steps.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: '1',
                title: 'Choose Your Mentor',
                description: 'Browse our curated list of space technology experts and find the perfect match for your goals.',
                icon: Users,
              },
              {
                step: '2',
                title: 'Book a Session',
                description: 'Schedule a 1-on-1 video call at a time that works for both you and your mentor.',
                icon: Calendar,
              },
              {
                step: '3',
                title: 'Get Guidance',
                description: 'Receive personalized advice, career guidance, and industry insights to accelerate your growth.',
                icon: MessageSquare,
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="bg-purple-600 text-white w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-4 text-sm font-bold">
                  {item.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold mb-4">
              Ready to Accelerate Your Career?
            </h2>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Join thousands of learners who have advanced their careers through expert mentorship.
            </p>
            <button className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-2 mx-auto">
              <span>Become a Mentor</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>
    </>
  );

  // Render with appropriate layout based on context
  if (isDashboardContext) {
    switch (dashboardContext) {
      case 'student':
        return (
          <StudentLayout>
            <MentorshipContent />
          </StudentLayout>
        );
      case 'organisation':
        return (
          <OrganisationLayout>
            <MentorshipContent />
          </OrganisationLayout>
        );
      case 'admin':
        return (
          <AdminLayout>
            <MentorshipContent />
          </AdminLayout>
        );
      default:
        return <MentorshipContent />;
    }
  }

  // Default standalone page layout
  return <MentorshipContent />;
}
