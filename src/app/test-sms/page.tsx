'use client';

import React, { useState } from 'react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import PhoneInput from '@/components/ui/PhoneInput';
import { Send, CheckCircle, XCircle } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TestSMSPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [message, setMessage] = useState('Hello from Nova! This is a test message.');
  const [isPhoneValid, setIsPhoneValid] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);

  const handleSendSMS = async () => {
    if (!phoneNumber || !message) {
      toast.error('Please enter both phone number and message');
      return;
    }

    if (!isPhoneValid) {
      toast.error('Please enter a valid Kenyan phone number');
      return;
    }

    setIsSending(true);
    try {
      const response = await fetch('/api/sms/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber,
          message,
        }),
      });

      const data = await response.json();
      setLastResult(data);

      if (data.success) {
        toast.success('SMS sent successfully!');
      } else {
        toast.error(data.error || 'Failed to send SMS');
      }
    } catch (error) {
      console.error('Error sending SMS:', error);
      toast.error('Failed to send SMS');
      setLastResult({ success: false, error: 'Network error' });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 pb-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">SMS Test</h1>
              <p className="text-gray-600">
                Test SMS functionality with AFTKNG sender ID and Kenyan phone number validation
              </p>
            </div>

            <div className="space-y-6">
              {/* Phone Number Input */}
              <PhoneInput
                value={phoneNumber}
                onChange={setPhoneNumber}
                onValidationChange={setIsPhoneValid}
                label="Phone Number"
                required={true}
                showValidation={true}
              />

              {/* Message Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Message
                </label>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your test message..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Message will be prefixed with "Test SMS from Nova: "
                </p>
              </div>

              {/* Send Button */}
              <button
                onClick={handleSendSMS}
                disabled={isSending || !isPhoneValid || !phoneNumber || !message}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                {isSending ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    <Send className="h-5 w-5" />
                    <span>Send Test SMS</span>
                  </>
                )}
              </button>

              {/* Result Display */}
              {lastResult && (
                <div className={`p-4 rounded-lg border ${
                  lastResult.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center mb-2">
                    {lastResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600 mr-2" />
                    )}
                    <h3 className={`font-medium ${
                      lastResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {lastResult.success ? 'SMS Sent Successfully' : 'SMS Failed'}
                    </h3>
                  </div>
                  
                  {lastResult.success && lastResult.data && (
                    <div className="text-sm text-green-700 space-y-1">
                      <p><strong>Phone:</strong> {lastResult.data.phoneNumber}</p>
                      <p><strong>Sender ID:</strong> {lastResult.data.senderId}</p>
                    </div>
                  )}
                  
                  {!lastResult.success && (
                    <p className="text-sm text-red-700">
                      <strong>Error:</strong> {lastResult.error}
                    </p>
                  )}
                </div>
              )}

              {/* Configuration Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-800 mb-2">SMS Configuration</h3>
                <div className="text-sm text-blue-700 space-y-1">
                  <p><strong>Sender ID:</strong> AFTKNG</p>
                  <p><strong>Provider:</strong> Africa's Talking</p>
                  <p><strong>Supported Format:</strong> +254 XXXXXXXXX (Kenyan numbers only)</p>
                  <p><strong>Validation:</strong> Real-time phone number format validation</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
