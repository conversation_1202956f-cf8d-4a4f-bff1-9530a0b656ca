import mongoose from 'mongoose';
import { MongoClient } from 'mongodb';

declare global {
  var mongoose: {
    conn: typeof import('mongoose') | null;
    promise: Promise<typeof import('mongoose')> | null;
  };
  var _mongoClientPromise: Promise<MongoClient> | undefined;

  namespace NodeJS {
    interface ProcessEnv {
      MONGODB_URI: string;
      GEMINI_API_KEY: string;
      GEMINI_ENDPOINT: string;
      AFRICASTALKING_USERNAME: string;
      AFRICASTALKING_API_KEY: string;
      NEXTAUTH_URL: string;
      NEXTAUTH_SECRET: string;
      JWT_SECRET: string;
      NODE_ENV: 'development' | 'production' | 'test';

      // Seed data configuration
      SEED_SKIP_RESOURCES?: string;
      SEED_SKIP_MENTOR_PROFILES?: string;
      SEED_MINIMAL_MODE?: string;
    }
  }
}

export {};
