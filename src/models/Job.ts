import mongoose, { Document, Schema } from 'mongoose';

export interface I<PERSON>ob extends Document {
  _id: string;
  title: string;
  company: string;
  companyLogo?: string;
  employerId: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  type: 'full-time' | 'part-time' | 'contract' | 'internship' | 'apprenticeship';
  location: {
    type: 'remote' | 'on-site' | 'hybrid';
    city?: string;
    country?: string;
    address?: string;
  };
  salary: {
    min?: number;
    max?: number;
    currency: string;
    period: 'hourly' | 'monthly' | 'yearly';
  };
  category: string;
  tags: string[];
  skillsRequired: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  education: string[];
  benefits: string[];
  applicationDeadline?: Date;
  startDate?: Date;
  isActive: boolean;
  isFeatured: boolean;
  applications: {
    total: number;
    shortlisted: number;
    interviewed: number;
    hired: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const JobSchema = new Schema<IJob>({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  company: {
    type: String,
    required: true,
    trim: true,
  },
  companyLogo: String,
  employerId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  requirements: [String],
  responsibilities: [String],
  type: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'internship', 'apprenticeship'],
    required: true,
  },
  location: {
    type: {
      type: String,
      enum: ['remote', 'on-site', 'hybrid'],
      required: true,
    },
    city: String,
    country: String,
    address: String,
  },
  salary: {
    min: Number,
    max: Number,
    currency: { type: String, default: 'USD' },
    period: {
      type: String,
      enum: ['hourly', 'monthly', 'yearly'],
      default: 'yearly',
    },
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Space Technology',
      'Satellite Engineering',
      'AI & Machine Learning',
      'Cybersecurity',
      'Data Science',
      'Software Engineering',
      'Robotics',
      'Research & Development',
      'Business & Management',
      'Marketing & Communications',
      'Other'
    ],
  },
  tags: [String],
  skillsRequired: [String],
  experienceLevel: {
    type: String,
    enum: ['entry', 'mid', 'senior', 'executive'],
    required: true,
  },
  education: [String],
  benefits: [String],
  applicationDeadline: Date,
  startDate: Date,
  isActive: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  applications: {
    total: { type: Number, default: 0 },
    shortlisted: { type: Number, default: 0 },
    interviewed: { type: Number, default: 0 },
    hired: { type: Number, default: 0 },
  },
}, {
  timestamps: true,
});

// Indexes for better query performance
JobSchema.index({ category: 1 });
JobSchema.index({ type: 1 });
JobSchema.index({ experienceLevel: 1 });
JobSchema.index({ 'location.type': 1 });
JobSchema.index({ isActive: 1 });
JobSchema.index({ isFeatured: -1 });
JobSchema.index({ createdAt: -1 });
JobSchema.index({ skillsRequired: 1 });

const Job = mongoose.models.Job || mongoose.model<IJob>('Job', JobSchema);

export default Job;
export { Job };
