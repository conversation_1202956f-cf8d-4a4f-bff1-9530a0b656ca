import mongoose, { Document, Schema } from 'mongoose';

export interface IJobApplication extends Document {
  _id: string;
  jobId: string;
  applicantId: string;
  employerId: string;
  status: 'applied' | 'under_review' | 'shortlisted' | 'interview_scheduled' | 'rejected' | 'hired';
  coverLetter?: string;
  resumeUrl?: string;
  portfolioUrl?: string;
  answers?: {
    question: string;
    answer: string;
  }[];
  appliedAt: Date;
  lastUpdated: Date;
  notes?: string;
  interviewDetails?: {
    scheduledAt?: Date;
    type?: 'phone' | 'video' | 'in_person';
    location?: string;
    meetingLink?: string;
    notes?: string;
  };
  feedback?: {
    rating?: number;
    comments?: string;
    providedBy?: string;
    providedAt?: Date;
  };
}

const JobApplicationSchema = new Schema<IJobApplication>({
  jobId: {
    type: Schema.Types.ObjectId,
    ref: 'Job',
    required: true,
  },
  applicantId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  employerId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  status: {
    type: String,
    enum: ['applied', 'under_review', 'shortlisted', 'interview_scheduled', 'rejected', 'hired'],
    default: 'applied',
  },
  coverLetter: String,
  resumeUrl: String,
  portfolioUrl: String,
  answers: [{
    question: String,
    answer: String,
  }],
  appliedAt: {
    type: Date,
    default: Date.now,
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
  notes: String,
  interviewDetails: {
    scheduledAt: Date,
    type: {
      type: String,
      enum: ['phone', 'video', 'in_person'],
    },
    location: String,
    meetingLink: String,
    notes: String,
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5,
    },
    comments: String,
    providedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    providedAt: Date,
  },
}, {
  timestamps: true,
});

// Compound index to ensure one application per user per job
JobApplicationSchema.index({ jobId: 1, applicantId: 1 }, { unique: true });
JobApplicationSchema.index({ employerId: 1, status: 1 });
JobApplicationSchema.index({ applicantId: 1 });
JobApplicationSchema.index({ appliedAt: -1 });

const JobApplication = mongoose.models.JobApplication || mongoose.model<IJobApplication>('JobApplication', JobApplicationSchema);

export default JobApplication;
export { JobApplication };
