import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  _id: string;
  email: string;
  password?: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'student' | 'mentor' | 'organisation' | 'admin';
  profile: {
    bio?: string;
    skills: string[];
    interests: string[];
    education?: string;
    experience?: string;
    location?: string;
    careerGoals: string[];
    currentLevel: 'beginner' | 'intermediate' | 'advanced';
  };
  preferences: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    language: string;
    timezone: string;
  };
  progress: {
    coursesCompleted: number;
    certificatesEarned: number;
    skillsAcquired: string[];
    totalLearningHours: number;
  };
  mentorship?: {
    isMentor: boolean;
    isMentee: boolean;
    expertise?: string[];
    specialization?: string;
    availability?: string[];
    mentees?: string[];
    mentors?: string[];
  };
  employment?: {
    isEmployer: boolean;
    company?: string;
    position?: string;
    jobsPosted?: number;
  };
  isVerified: boolean;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    select: false, // Don't include password in queries by default
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  avatar: {
    type: String,
  },
  role: {
    type: String,
    enum: ['student', 'mentor', 'organisation', 'admin'],
    default: 'student',
  },
  roleHistory: [{
    previousRole: String,
    newRole: String,
    changedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    changedAt: { type: Date, default: Date.now },
    reason: String,
  }],
  profile: {
    bio: String,
    skills: [String],
    interests: [String],
    education: String,
    experience: String,
    location: String,
    careerGoals: [String],
    currentLevel: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner',
    },
  },
  preferences: {
    notifications: {
      email: { type: Boolean, default: false },
      sms: { type: Boolean, default: true },
      push: { type: Boolean, default: false },
    },
    language: { type: String, default: 'en' },
    timezone: { type: String, default: 'UTC' },
  },
  progress: {
    coursesCompleted: { type: Number, default: 0 },
    certificatesEarned: { type: Number, default: 0 },
    skillsAcquired: [String],
    totalLearningHours: { type: Number, default: 0 },
  },
  mentorship: {
    isMentor: { type: Boolean, default: false },
    isMentee: { type: Boolean, default: false },
    isApproved: { type: Boolean, default: false },
    appliedAt: Date,
    approvedAt: Date,
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    expertise: [String],
    specialization: String,
    availability: [String],
    mentees: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    mentors: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  },
  employment: {
    isEmployer: { type: Boolean, default: false },
    companyVerified: { type: Boolean, default: false },
    verifiedAt: Date,
    verifiedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    company: String,
    position: String,
    jobsPosted: { type: Number, default: 0 },
    companyInfo: {
      name: String,
      website: String,
      size: String,
      industry: String,
    },
  },
  partnership: {
    isPartner: { type: Boolean, default: false },
    isApproved: { type: Boolean, default: false },
    appliedAt: Date,
    approvedAt: Date,
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    organizationInfo: {
      name: String,
      type: String,
      region: String,
      website: String,
    },
  },
  isVerified: { type: Boolean, default: false },
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
}, {
  timestamps: true,
});

// Indexes for better query performance (email already has unique index)
UserSchema.index({ role: 1 });
UserSchema.index({ 'profile.skills': 1 });
UserSchema.index({ 'profile.interests': 1 });
UserSchema.index({ isActive: 1 });

// Create model with proper error handling for Next.js hot reloading
const User: mongoose.Model<IUser> =
  mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
export { User };
