import mongoose, { Document, Schema } from 'mongoose';

export interface IResource extends Document {
  _id: string;
  title: string;
  description: string;
  type: 'pdf' | 'video' | 'audio' | 'document' | 'link' | 'image' | 'presentation' | 'dataset';
  category: string;
  subcategory?: string;
  tags: string[];
  author: {
    name: string;
    organization?: string;
    email?: string;
    userId?: string;
  };
  file: {
    originalName: string;
    fileName: string;
    url: string;
    size: number; // in bytes
    mimeType: string;
    uploadedAt: Date;
  };
  metadata: {
    duration?: number; // for videos/audio in seconds
    pages?: number; // for documents
    resolution?: string; // for images/videos
    language: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'all';
    subject: string;
  };
  access: {
    visibility: 'public' | 'members_only' | 'premium' | 'private';
    allowedRoles: string[];
    requiredLevel?: 'beginner' | 'intermediate' | 'advanced';
    price?: number;
    currency?: string;
    isFree: boolean;
  };
  engagement: {
    views: number;
    downloads: number;
    likes: string[]; // Array of user IDs
    bookmarks: string[]; // Array of user IDs
    shares: number;
    averageRating: number;
    totalRatings: number;
    comments: number;
  };
  reviews: {
    userId: string;
    rating: number;
    comment?: string;
    createdAt: Date;
  }[];
  relatedResources: string[]; // Array of resource IDs
  prerequisites: string[];
  learningOutcomes: string[];
  status: 'draft' | 'published' | 'archived' | 'under_review';
  moderation: {
    isApproved: boolean;
    approvedBy?: string;
    approvedAt?: Date;
    rejectionReason?: string;
    flags: {
      flaggedBy: string;
      reason: string;
      flaggedAt: Date;
    }[];
  };
  analytics: {
    weeklyViews: number;
    monthlyViews: number;
    totalViews: number;
    conversionRate?: number; // for premium resources
    popularityScore: number;
  };
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IResourceCollection extends Document {
  _id: string;
  name: string;
  description: string;
  slug: string;
  thumbnail?: string;
  category: string;
  tags: string[];
  resources: string[]; // Array of resource IDs
  curator: {
    name: string;
    userId: string;
    organization?: string;
  };
  access: {
    visibility: 'public' | 'members_only' | 'premium' | 'private';
    allowedRoles: string[];
    price?: number;
    currency?: string;
    isFree: boolean;
  };
  metadata: {
    totalResources: number;
    totalSize: number; // in bytes
    estimatedTime: number; // in hours
    level: 'beginner' | 'intermediate' | 'advanced' | 'mixed';
    lastUpdated: Date;
  };
  engagement: {
    subscribers: string[]; // Array of user IDs
    views: number;
    likes: string[]; // Array of user IDs
    shares: number;
    averageRating: number;
    totalRatings: number;
  };
  isPublished: boolean;
  isFeatured: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IResourceBookmark extends Document {
  _id: string;
  userId: string;
  resourceId?: string;
  collectionId?: string;
  type: 'resource' | 'collection';
  folder?: string;
  notes?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

const ResourceSchema = new Schema<IResource>({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000,
  },
  type: {
    type: String,
    enum: ['pdf', 'video', 'audio', 'document', 'link', 'image', 'presentation', 'dataset'],
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  subcategory: String,
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  author: {
    name: { type: String, required: true },
    organization: String,
    email: String,
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  file: {
    originalName: { type: String, required: true },
    fileName: { type: String, required: true },
    url: { type: String, required: true },
    size: { type: Number, required: true },
    mimeType: { type: String, required: true },
    uploadedAt: { type: Date, default: Date.now },
  },
  metadata: {
    duration: Number,
    pages: Number,
    resolution: String,
    language: { type: String, default: 'en' },
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'all'],
      default: 'all',
    },
    subject: { type: String, required: true },
  },
  access: {
    visibility: {
      type: String,
      enum: ['public', 'members_only', 'premium', 'private'],
      default: 'public',
    },
    allowedRoles: [{
      type: String,
      enum: ['student', 'mentor', 'employer', 'admin', 'partner'],
    }],
    requiredLevel: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
    },
    price: Number,
    currency: { type: String, default: 'USD' },
    isFree: { type: Boolean, default: true },
  },
  engagement: {
    views: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    likes: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    bookmarks: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
  },
  reviews: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      required: true,
    },
    comment: String,
    createdAt: { type: Date, default: Date.now },
  }],
  relatedResources: [{
    type: Schema.Types.ObjectId,
    ref: 'Resource',
  }],
  prerequisites: [String],
  learningOutcomes: [String],
  status: {
    type: String,
    enum: ['draft', 'published', 'archived', 'under_review'],
    default: 'draft',
  },
  moderation: {
    isApproved: { type: Boolean, default: false },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    approvedAt: Date,
    rejectionReason: String,
    flags: [{
      flaggedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
      },
      reason: String,
      flaggedAt: { type: Date, default: Date.now },
    }],
  },
  analytics: {
    weeklyViews: { type: Number, default: 0 },
    monthlyViews: { type: Number, default: 0 },
    totalViews: { type: Number, default: 0 },
    conversionRate: Number,
    popularityScore: { type: Number, default: 0 },
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

const ResourceCollectionSchema = new Schema<IResourceCollection>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  thumbnail: String,
  category: {
    type: String,
    required: true,
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  resources: [{
    type: Schema.Types.ObjectId,
    ref: 'Resource',
  }],
  curator: {
    name: { type: String, required: true },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    organization: String,
  },
  access: {
    visibility: {
      type: String,
      enum: ['public', 'members_only', 'premium', 'private'],
      default: 'public',
    },
    allowedRoles: [{
      type: String,
      enum: ['student', 'mentor', 'employer', 'admin', 'partner'],
    }],
    price: Number,
    currency: { type: String, default: 'USD' },
    isFree: { type: Boolean, default: true },
  },
  metadata: {
    totalResources: { type: Number, default: 0 },
    totalSize: { type: Number, default: 0 },
    estimatedTime: { type: Number, default: 0 },
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'mixed'],
      default: 'mixed',
    },
    lastUpdated: { type: Date, default: Date.now },
  },
  engagement: {
    subscribers: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    views: { type: Number, default: 0 },
    likes: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
  },
  isPublished: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

const ResourceBookmarkSchema = new Schema<IResourceBookmark>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  resourceId: {
    type: Schema.Types.ObjectId,
    ref: 'Resource',
  },
  collectionId: {
    type: Schema.Types.ObjectId,
    ref: 'ResourceCollection',
  },
  type: {
    type: String,
    enum: ['resource', 'collection'],
    required: true,
  },
  folder: String,
  notes: String,
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
}, {
  timestamps: true,
});

// Indexes
ResourceSchema.index({ category: 1, status: 1 });
ResourceSchema.index({ type: 1 });
ResourceSchema.index({ tags: 1 });
ResourceSchema.index({ 'access.visibility': 1 });
ResourceSchema.index({ createdBy: 1 });
ResourceSchema.index({ 'engagement.averageRating': -1 });
ResourceSchema.index({ 'analytics.popularityScore': -1 });

// slug already has unique index
ResourceCollectionSchema.index({ category: 1 });
ResourceCollectionSchema.index({ isPublished: 1, isFeatured: 1 });
ResourceCollectionSchema.index({ createdBy: 1 });

ResourceBookmarkSchema.index({ userId: 1, type: 1 });
ResourceBookmarkSchema.index({ resourceId: 1 });
ResourceBookmarkSchema.index({ collectionId: 1 });

export const Resource = mongoose.models.Resource || mongoose.model<IResource>('Resource', ResourceSchema);
export const ResourceCollection = mongoose.models.ResourceCollection || mongoose.model<IResourceCollection>('ResourceCollection', ResourceCollectionSchema);
export const ResourceBookmark = mongoose.models.ResourceBookmark || mongoose.model<IResourceBookmark>('ResourceBookmark', ResourceBookmarkSchema);
