import mongoose, { Document, Schema } from 'mongoose';

export interface IEvent extends Document {
  _id: string;
  title: string;
  description: string;
  type: 'webinar' | 'workshop' | 'conference' | 'job_fair' | 'networking' | 'competition' | 'meetup';
  category: string;
  organizer: {
    name: string;
    organization?: string;
    email: string;
    userId?: string;
  };
  speakers: {
    name: string;
    title: string;
    organization?: string;
    bio?: string;
    avatar?: string;
  }[];
  schedule: {
    startDate: Date;
    endDate: Date;
    timezone: string;
    sessions?: {
      title: string;
      description?: string;
      startTime: Date;
      endTime: Date;
      speaker?: string;
    }[];
  };
  location: {
    type: 'online' | 'in_person' | 'hybrid';
    venue?: string;
    address?: string;
    city?: string;
    country?: string;
    meetingLink?: string;
    platform?: string;
  };
  registration: {
    isRequired: boolean;
    maxAttendees?: number;
    currentAttendees: number;
    deadline?: Date;
    fee?: number;
    currency?: string;
    isFree: boolean;
    requiresApproval: boolean;
  };
  content: {
    agenda?: string;
    materials?: {
      title: string;
      type: 'pdf' | 'video' | 'link' | 'document';
      url: string;
    }[];
    prerequisites?: string[];
    learningOutcomes?: string[];
  };
  tags: string[];
  thumbnail?: string;
  images?: string[];
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  visibility: 'public' | 'private' | 'members_only';
  featured: boolean;
  attendees: {
    userId: string;
    registeredAt: Date;
    status: 'registered' | 'confirmed' | 'attended' | 'no_show' | 'cancelled';
    feedback?: {
      rating: number;
      review: string;
    };
  }[];
  reminders: {
    sent24h: boolean;
    sent1h: boolean;
    sentStart: boolean;
  };
  analytics: {
    views: number;
    registrations: number;
    attendanceRate: number;
    averageRating: number;
    totalRatings: number;
  };
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IEventRegistration extends Document {
  _id: string;
  eventId: string;
  userId: string;
  registeredAt: Date;
  status: 'registered' | 'confirmed' | 'attended' | 'no_show' | 'cancelled';
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
  specialRequests?: string;
  feedback?: {
    rating: number;
    review: string;
    submittedAt: Date;
  };
  remindersSent: {
    registration: boolean;
    day_before: boolean;
    hour_before: boolean;
    start: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const EventSchema = new Schema<IEvent>({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['webinar', 'workshop', 'conference', 'job_fair', 'networking', 'competition', 'meetup'],
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  organizer: {
    name: { type: String, required: true },
    organization: String,
    email: { type: String, required: true },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  speakers: [{
    name: { type: String, required: true },
    title: String,
    organization: String,
    bio: String,
    avatar: String,
  }],
  schedule: {
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    timezone: { type: String, required: true },
    sessions: [{
      title: String,
      description: String,
      startTime: Date,
      endTime: Date,
      speaker: String,
    }],
  },
  location: {
    type: {
      type: String,
      enum: ['online', 'in_person', 'hybrid'],
      required: true,
    },
    venue: String,
    address: String,
    city: String,
    country: String,
    meetingLink: String,
    platform: String,
  },
  registration: {
    isRequired: { type: Boolean, default: true },
    maxAttendees: Number,
    currentAttendees: { type: Number, default: 0 },
    deadline: Date,
    fee: Number,
    currency: { type: String, default: 'USD' },
    isFree: { type: Boolean, default: true },
    requiresApproval: { type: Boolean, default: false },
  },
  content: {
    agenda: String,
    materials: [{
      title: String,
      type: {
        type: String,
        enum: ['pdf', 'video', 'link', 'document'],
      },
      url: String,
    }],
    prerequisites: [String],
    learningOutcomes: [String],
  },
  tags: [String],
  thumbnail: String,
  images: [String],
  status: {
    type: String,
    enum: ['draft', 'published', 'cancelled', 'completed'],
    default: 'draft',
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'members_only'],
    default: 'public',
  },
  featured: { type: Boolean, default: false },
  attendees: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    registeredAt: { type: Date, default: Date.now },
    status: {
      type: String,
      enum: ['registered', 'confirmed', 'attended', 'no_show', 'cancelled'],
      default: 'registered',
    },
    feedback: {
      rating: {
        type: Number,
        min: 1,
        max: 5,
      },
      review: String,
    },
  }],
  reminders: {
    sent24h: { type: Boolean, default: false },
    sent1h: { type: Boolean, default: false },
    sentStart: { type: Boolean, default: false },
  },
  analytics: {
    views: { type: Number, default: 0 },
    registrations: { type: Number, default: 0 },
    attendanceRate: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
  },
  isRecurring: { type: Boolean, default: false },
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
    },
    interval: Number,
    endDate: Date,
  },
}, {
  timestamps: true,
});

const EventRegistrationSchema = new Schema<IEventRegistration>({
  eventId: {
    type: Schema.Types.ObjectId,
    ref: 'Event',
    required: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  registeredAt: {
    type: Date,
    default: Date.now,
  },
  status: {
    type: String,
    enum: ['registered', 'confirmed', 'attended', 'no_show', 'cancelled'],
    default: 'registered',
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
  },
  specialRequests: String,
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5,
    },
    review: String,
    submittedAt: Date,
  },
  remindersSent: {
    registration: { type: Boolean, default: false },
    day_before: { type: Boolean, default: false },
    hour_before: { type: Boolean, default: false },
    start: { type: Boolean, default: false },
  },
}, {
  timestamps: true,
});

// Indexes
EventSchema.index({ type: 1, status: 1 });
EventSchema.index({ 'schedule.startDate': 1 });
EventSchema.index({ category: 1 });
EventSchema.index({ featured: 1, status: 1 });
EventSchema.index({ tags: 1 });
EventSchema.index({ 'organizer.userId': 1 });

EventRegistrationSchema.index({ eventId: 1, userId: 1 }, { unique: true });
EventRegistrationSchema.index({ userId: 1 });
EventRegistrationSchema.index({ status: 1 });

export const Event = mongoose.models.Event || mongoose.model<IEvent>('Event', EventSchema);
export const EventRegistration = mongoose.models.EventRegistration || mongoose.model<IEventRegistration>('EventRegistration', EventRegistrationSchema);
