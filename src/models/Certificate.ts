import mongoose, { Document, Schema } from 'mongoose';

export interface ICertificate extends Document {
  _id: string;
  certificateId: string; // Unique certificate identifier
  userId: string;
  courseId?: string;
  programId?: string;
  type: 'course_completion' | 'skill_assessment' | 'program_completion' | 'achievement' | 'participation';
  title: string;
  description: string;
  issuer: {
    name: string;
    organization: string;
    logo?: string;
    signature?: string;
  };
  recipient: {
    name: string;
    email: string;
    userId: string;
  };
  skills: string[];
  competencies: {
    name: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    verified: boolean;
  }[];
  assessment: {
    score?: number;
    maxScore?: number;
    percentage?: number;
    passingScore: number;
    assessmentDate: Date;
    assessmentType: 'quiz' | 'project' | 'practical' | 'comprehensive';
  };
  verification: {
    blockchainHash?: string;
    verificationUrl: string;
    isVerified: boolean;
    verifiedAt?: Date;
    verificationMethod: 'blockchain' | 'digital_signature' | 'database';
  };
  metadata: {
    duration?: number; // Course duration in hours
    completionDate: Date;
    expiryDate?: Date;
    creditsEarned?: number;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    category: string;
  };
  design: {
    templateId: string;
    backgroundColor: string;
    textColor: string;
    logoPosition: 'top' | 'bottom' | 'left' | 'right';
    customFields?: {
      [key: string]: string;
    };
  };
  files: {
    pdfUrl?: string;
    imageUrl?: string;
    badgeUrl?: string;
  };
  sharing: {
    isPublic: boolean;
    linkedInUrl?: string;
    socialMediaShared: boolean;
    portfolioIncluded: boolean;
  };
  status: 'draft' | 'issued' | 'revoked' | 'expired';
  analytics: {
    views: number;
    downloads: number;
    verifications: number;
    shares: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ISkillAssessment extends Document {
  _id: string;
  userId: string;
  skillArea: string;
  assessmentType: 'quiz' | 'practical' | 'project' | 'interview';
  questions: {
    question: string;
    type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'code';
    options?: string[];
    correctAnswer?: string | string[];
    userAnswer?: string | string[];
    points: number;
    difficulty: 'easy' | 'medium' | 'hard';
  }[];
  results: {
    totalQuestions: number;
    correctAnswers: number;
    score: number;
    percentage: number;
    timeSpent: number; // in minutes
    skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
  };
  aiAnalysis?: {
    detailedFeedback: string;
    learningPath: string[];
    nextSteps: string[];
    estimatedImprovementTime: number; // in weeks
  };
  status: 'in_progress' | 'completed' | 'expired';
  startedAt: Date;
  completedAt?: Date;
  expiresAt: Date;
  certificateIssued: boolean;
  certificateId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICertificateTemplate extends Document {
  _id: string;
  name: string;
  description: string;
  type: 'course_completion' | 'skill_assessment' | 'program_completion' | 'achievement' | 'participation';
  design: {
    layout: 'portrait' | 'landscape';
    backgroundColor: string;
    backgroundImage?: string;
    textColor: string;
    accentColor: string;
    fontFamily: string;
    logoPosition: 'top' | 'bottom' | 'left' | 'right';
    borderStyle?: string;
  };
  fields: {
    recipientName: { x: number; y: number; fontSize: number; fontWeight: string };
    title: { x: number; y: number; fontSize: number; fontWeight: string };
    description: { x: number; y: number; fontSize: number; fontWeight: string };
    issueDate: { x: number; y: number; fontSize: number; fontWeight: string };
    signature: { x: number; y: number; width: number; height: number };
    logo: { x: number; y: number; width: number; height: number };
    qrCode: { x: number; y: number; size: number };
  };
  isActive: boolean;
  isDefault: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

const CertificateSchema = new Schema<ICertificate>({
  certificateId: {
    type: String,
    required: true,
    unique: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  courseId: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
  },
  programId: {
    type: Schema.Types.ObjectId,
    ref: 'MentorshipProgram',
  },
  type: {
    type: String,
    enum: ['course_completion', 'skill_assessment', 'program_completion', 'achievement', 'participation'],
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  issuer: {
    name: { type: String, required: true },
    organization: { type: String, required: true },
    logo: String,
    signature: String,
  },
  recipient: {
    name: { type: String, required: true },
    email: { type: String, required: true },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  skills: [String],
  competencies: [{
    name: String,
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    },
    verified: { type: Boolean, default: false },
  }],
  assessment: {
    score: Number,
    maxScore: Number,
    percentage: Number,
    passingScore: { type: Number, required: true },
    assessmentDate: { type: Date, required: true },
    assessmentType: {
      type: String,
      enum: ['quiz', 'project', 'practical', 'comprehensive'],
    },
  },
  verification: {
    blockchainHash: String,
    verificationUrl: { type: String, required: true },
    isVerified: { type: Boolean, default: false },
    verifiedAt: Date,
    verificationMethod: {
      type: String,
      enum: ['blockchain', 'digital_signature', 'database'],
      default: 'database',
    },
  },
  metadata: {
    duration: Number,
    completionDate: { type: Date, required: true },
    expiryDate: Date,
    creditsEarned: Number,
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
      required: true,
    },
    category: { type: String, required: true },
  },
  design: {
    templateId: { type: String, required: true },
    backgroundColor: { type: String, default: '#ffffff' },
    textColor: { type: String, default: '#000000' },
    logoPosition: {
      type: String,
      enum: ['top', 'bottom', 'left', 'right'],
      default: 'top',
    },
    customFields: Schema.Types.Mixed,
  },
  files: {
    pdfUrl: String,
    imageUrl: String,
    badgeUrl: String,
  },
  sharing: {
    isPublic: { type: Boolean, default: true },
    linkedInUrl: String,
    socialMediaShared: { type: Boolean, default: false },
    portfolioIncluded: { type: Boolean, default: true },
  },
  status: {
    type: String,
    enum: ['draft', 'issued', 'revoked', 'expired'],
    default: 'draft',
  },
  analytics: {
    views: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    verifications: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
  },
}, {
  timestamps: true,
});

const SkillAssessmentSchema = new Schema<ISkillAssessment>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  skillArea: {
    type: String,
    required: true,
  },
  assessmentType: {
    type: String,
    enum: ['quiz', 'practical', 'project', 'interview'],
    required: true,
  },
  questions: [{
    question: { type: String, required: true },
    type: {
      type: String,
      enum: ['multiple_choice', 'true_false', 'short_answer', 'essay', 'code'],
      required: true,
    },
    options: [String],
    correctAnswer: Schema.Types.Mixed,
    userAnswer: Schema.Types.Mixed,
    points: { type: Number, required: true },
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      required: true,
    },
  }],
  results: {
    totalQuestions: Number,
    correctAnswers: Number,
    score: Number,
    percentage: Number,
    timeSpent: Number,
    skillLevel: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    },
    strengths: [String],
    weaknesses: [String],
    recommendations: [String],
  },
  aiAnalysis: {
    detailedFeedback: String,
    learningPath: [String],
    nextSteps: [String],
    estimatedImprovementTime: Number,
  },
  status: {
    type: String,
    enum: ['in_progress', 'completed', 'expired'],
    default: 'in_progress',
  },
  startedAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: Date,
  expiresAt: {
    type: Date,
    required: true,
  },
  certificateIssued: {
    type: Boolean,
    default: false,
  },
  certificateId: String,
}, {
  timestamps: true,
});

const CertificateTemplateSchema = new Schema<ICertificateTemplate>({
  name: {
    type: String,
    required: true,
  },
  description: String,
  type: {
    type: String,
    enum: ['course_completion', 'skill_assessment', 'program_completion', 'achievement', 'participation'],
    required: true,
  },
  design: {
    layout: {
      type: String,
      enum: ['portrait', 'landscape'],
      default: 'landscape',
    },
    backgroundColor: { type: String, default: '#ffffff' },
    backgroundImage: String,
    textColor: { type: String, default: '#000000' },
    accentColor: { type: String, default: '#3b82f6' },
    fontFamily: { type: String, default: 'Arial' },
    logoPosition: {
      type: String,
      enum: ['top', 'bottom', 'left', 'right'],
      default: 'top',
    },
    borderStyle: String,
  },
  fields: {
    recipientName: {
      x: Number,
      y: Number,
      fontSize: Number,
      fontWeight: String,
    },
    title: {
      x: Number,
      y: Number,
      fontSize: Number,
      fontWeight: String,
    },
    description: {
      x: Number,
      y: Number,
      fontSize: Number,
      fontWeight: String,
    },
    issueDate: {
      x: Number,
      y: Number,
      fontSize: Number,
      fontWeight: String,
    },
    signature: {
      x: Number,
      y: Number,
      width: Number,
      height: Number,
    },
    logo: {
      x: Number,
      y: Number,
      width: Number,
      height: Number,
    },
    qrCode: {
      x: Number,
      y: Number,
      size: Number,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isDefault: {
    type: Boolean,
    default: false,
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

// Indexes (certificateId already has unique index)
CertificateSchema.index({ userId: 1 });
CertificateSchema.index({ courseId: 1 });
CertificateSchema.index({ type: 1, status: 1 });
CertificateSchema.index({ 'verification.isVerified': 1 });

SkillAssessmentSchema.index({ userId: 1, skillArea: 1 });
SkillAssessmentSchema.index({ status: 1 });
SkillAssessmentSchema.index({ completedAt: 1 });

CertificateTemplateSchema.index({ type: 1, isActive: 1 });
CertificateTemplateSchema.index({ isDefault: 1 });

export const Certificate = mongoose.models.Certificate || mongoose.model<ICertificate>('Certificate', CertificateSchema);
export const SkillAssessment = mongoose.models.SkillAssessment || mongoose.model<ISkillAssessment>('SkillAssessment', SkillAssessmentSchema);
export const CertificateTemplate = mongoose.models.CertificateTemplate || mongoose.model<ICertificateTemplate>('CertificateTemplate', CertificateTemplateSchema);
