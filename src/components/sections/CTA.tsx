'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowR<PERSON>, Rocket, Star, Zap } from 'lucide-react';

export function CTA() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-orange-400/20 rounded-full animate-bounce" />
        <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-yellow-400/20 rounded-full animate-ping" />
        <div className="absolute bottom-10 right-10 w-20 h-20 bg-white/10 rounded-full animate-pulse" />
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 text-white text-sm font-medium mb-6 backdrop-blur-sm">
              <Star className="h-4 w-4 mr-2" />
              Limited Time: Free Access to Premium Courses
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
              Launch Your Future in
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                Space Technology
              </span>
            </h2>

            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join Africa's fastest-growing space technology community. Get access to expert-led courses,
              industry mentorship, and exclusive job opportunities that will accelerate your career.
            </p>

            <div className="flex flex-wrap justify-center gap-6 mb-10 text-white">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-yellow-400" />
                <span>Instant Access</span>
              </div>
              <div className="flex items-center space-x-2">
                <Rocket className="h-5 w-5 text-yellow-400" />
                <span>Expert Mentorship</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-400" />
                <span>Industry Certification</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
          >
            <Link
              href="/auth/signup"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-all duration-200 flex items-center justify-center space-x-2 group font-semibold text-lg shadow-lg"
            >
              <span>Start Learning Now</span>
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link
              href="/courses"
              className="border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-200 flex items-center justify-center space-x-2 font-semibold text-lg"
            >
              <span>Explore Courses</span>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          >
            <div className="text-white">
              <div className="text-2xl font-bold mb-1">10,000+</div>
              <div className="text-blue-200 text-sm">Active Learners</div>
            </div>
            <div className="text-white">
              <div className="text-2xl font-bold mb-1">200+</div>
              <div className="text-blue-200 text-sm">Expert Courses</div>
            </div>
            <div className="text-white">
              <div className="text-2xl font-bold mb-1">150+</div>
              <div className="text-blue-200 text-sm">Industry Mentors</div>
            </div>
            <div className="text-white">
              <div className="text-2xl font-bold mb-1">25</div>
              <div className="text-blue-200 text-sm">African Countries</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-12 text-blue-200 text-sm"
          >
            <p>
              🚀 Join the space revolution • 📱 SMS notifications included • 🎓 Industry-recognized certificates
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}