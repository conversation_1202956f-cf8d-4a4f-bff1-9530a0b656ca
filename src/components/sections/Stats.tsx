'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const stats = [
  {
    number: 10000,
    suffix: '+',
    label: 'Active Learners',
    description: 'Students from across Africa building space tech skills',
  },
  {
    number: 200,
    suffix: '+',
    label: 'Expert Courses',
    description: 'Comprehensive courses covering all aspects of space technology',
  },
  {
    number: 150,
    suffix: '+',
    label: 'Industry Mentors',
    description: 'Experienced professionals guiding the next generation',
  },
  {
    number: 500,
    suffix: '+',
    label: 'Certificates Issued',
    description: 'Industry-recognized credentials earned by our learners',
  },
  {
    number: 50,
    suffix: '+',
    label: 'Partner Companies',
    description: 'Leading organizations offering job opportunities',
  },
  {
    number: 25,
    suffix: '',
    label: 'African Countries',
    description: 'Represented in our growing community',
  },
];

function CountUpAnimation({ end, duration = 2000 }: { end: number; duration?: number }) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration]);

  return <span>{count.toLocaleString()}</span>;
}

export function Stats() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white relative overflow-hidden">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Powering Africa's
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              {" "}Space Revolution
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Join a thriving community that's shaping the future of space technology across the continent.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 hover:bg-white/20 transition-all duration-300 border border-white/20">
                <div className="text-4xl md:text-5xl font-bold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                  <CountUpAnimation end={stat.number} />
                  {stat.suffix}
                </div>

                <h3 className="text-xl font-semibold mb-3 text-white">
                  {stat.label}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {stat.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold mb-4">
              Be Part of Something Bigger
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Every learner, mentor, and partner contributes to building Africa's space technology ecosystem.
              Your journey starts here.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200">
                Join the Community
              </button>
              <button className="border border-white/30 text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors duration-200">
                Partner With Us
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}