'use client';

import React, { useState, useEffect } from 'react';
import { Phone } from 'lucide-react';
import { PhoneValidator } from '@/lib/phone-validation';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean, error?: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  label?: string;
  showValidation?: boolean;
  id?: string;
  name?: string;
}

export default function PhoneInput({
  value,
  onChange,
  onValidationChange,
  placeholder = "+254 700 000 000",
  required = false,
  disabled = false,
  className = "",
  label,
  showValidation = true,
  id,
  name,
}: PhoneInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [hasBeenTouched, setHasBeenTouched] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    error?: string;
  }>({ isValid: true });

  // Validate phone number whenever value changes
  useEffect(() => {
    if (value) {
      const result = PhoneValidator.validateAndFormat(value);
      setValidationResult({
        isValid: result.isValid,
        error: result.error,
      });
      
      // Notify parent component of validation status
      if (onValidationChange) {
        onValidationChange(result.isValid, result.error);
      }
    } else {
      setValidationResult({ isValid: !required });
      if (onValidationChange) {
        onValidationChange(!required);
      }
    }
  }, [value, required, onValidationChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Format as user types
    const formatted = PhoneValidator.formatAsUserTypes(inputValue);
    onChange(formatted);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasBeenTouched(true);
    
    // Format the final value when user finishes typing
    if (value) {
      const result = PhoneValidator.validateAndFormat(value);
      if (result.isValid) {
        onChange(result.formatted);
      }
    }
  };

  const shouldShowError = showValidation && hasBeenTouched && !isFocused && !validationResult.isValid && value;
  const shouldShowSuccess = showValidation && hasBeenTouched && validationResult.isValid && value;

  const inputClasses = `
    w-full pl-10 pr-4 py-3 border rounded-lg 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
    transition-colors duration-200
    ${shouldShowError ? 'border-red-500 bg-red-50' : ''}
    ${shouldShowSuccess ? 'border-green-500 bg-green-50' : ''}
    ${!shouldShowError && !shouldShowSuccess ? 'border-gray-300' : ''}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
    ${className}
  `.trim();

  return (
    <div className="space-y-2">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="tel"
          id={id}
          name={name}
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={inputClasses}
          autoComplete="tel"
        />
        
        {/* Validation indicator */}
        {showValidation && value && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {shouldShowSuccess && (
              <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center">
                <svg className="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            )}
            {shouldShowError && (
              <div className="h-5 w-5 rounded-full bg-red-500 flex items-center justify-center">
                <svg className="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Help text and error messages */}
      <div className="min-h-[1.25rem]">
        {shouldShowError && validationResult.error && (
          <p className="text-sm text-red-600 flex items-center">
            <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {validationResult.error}
          </p>
        )}
        {!shouldShowError && !value && (
          <p className="text-xs text-gray-500">
            Enter your Kenyan mobile number (e.g., +254 700 000 000)
          </p>
        )}
        {shouldShowSuccess && (
          <p className="text-sm text-green-600 flex items-center">
            <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Valid Kenyan mobile number
          </p>
        )}
      </div>
    </div>
  );
}
