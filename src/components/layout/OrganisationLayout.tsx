'use client';

import React from 'react';
import { OrganisationNavbar } from './OrganisationNavbar';

interface OrganisationLayoutProps {
  children: React.ReactNode;
  newApplications?: number;
  activeJobs?: number;
  activeCourses?: number;
  upcomingEvents?: number;
}

export function OrganisationLayout({
  children,
  newApplications = 0,
  activeJobs = 0,
  activeCourses = 0,
  upcomingEvents = 0
}: OrganisationLayoutProps) {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <OrganisationNavbar
        newApplications={newApplications}
        activeJobs={activeJobs}
        activeCourses={activeCourses}
        upcomingEvents={upcomingEvents}
      />

      {/* Main content */}
      <div className="lg:ml-64 relative z-10">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
