'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  BookOpen,
  Users,
  Calendar,
  MessageSquare,
  User,
  Award,
  TrendingUp,
  FileText,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  GraduationCap,
  Target,
  Clock,
  DollarSign,
  Star,
  Plus,
  HandHeart,
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavItem[];
}

interface MentorNavbarProps {
  totalMentees?: number;
  upcomingSessions?: number;
  coursesCreated?: number;
}

export function MentorNavbar({
  totalMentees = 0,
  upcomingSessions = 0,
  coursesCreated = 0
}: MentorNavbarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/mentor',
      icon: LayoutDashboard,
    },
    {
      name: 'Mentorship',
      href: '/dashboard/mentor/mentorship',
      icon: HandHeart,
      badge: upcomingSessions,
      children: [
        { name: 'My Mentees', href: '/dashboard/mentor/mentees', icon: Users },
        { name: 'Sessions', href: '/dashboard/mentor/sessions', icon: Calendar },
        { name: 'Availability', href: '/mentorship/availability', icon: Clock },
        { name: 'Find Mentees', href: '/mentorship/find-mentees', icon: Target },
      ],
    },
    {
      name: 'Education',
      href: '/dashboard/mentor/courses',
      icon: BookOpen,
      badge: coursesCreated,
      children: [
        { name: 'My Courses', href: '/dashboard/mentor/courses', icon: BookOpen },
        { name: 'Create Course', href: '/courses/create', icon: Plus },
        { name: 'Course Analytics', href: '/dashboard/mentor/course-analytics', icon: TrendingUp },
        { name: 'Certificates', href: '/certificates?from=mentor', icon: GraduationCap },
      ],
    },
    {
      name: 'Community',
      href: '/community?from=mentor',
      icon: MessageSquare,
      children: [
        { name: 'Forums', href: '/community/forums', icon: MessageSquare },
        { name: 'Events', href: '/events?from=mentor', icon: Calendar },
        { name: 'Create Event', href: '/events/create', icon: Plus },
        { name: 'Workshops', href: '/community/workshops', icon: Users },
      ],
    },
    {
      name: 'Earnings',
      href: '/dashboard/mentor/earnings',
      icon: DollarSign,
      children: [
        { name: 'Overview', href: '/dashboard/mentor/earnings', icon: DollarSign },
        { name: 'Transactions', href: '/dashboard/mentor/transactions', icon: FileText },
        { name: 'Payouts', href: '/dashboard/mentor/payouts', icon: TrendingUp },
      ],
    },
    {
      name: 'Reviews',
      href: '/dashboard/mentor/reviews',
      icon: Star,
    },
    {
      name: 'Profile',
      href: '/dashboard/mentor/profile',
      icon: User,
      children: [
        { name: 'My Profile', href: '/profile', icon: User },
        { name: 'Achievements', href: '/profile/achievements', icon: Award },
        { name: 'Settings', href: '/settings', icon: Settings },
      ],
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard/mentor') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const isChildActive = (children: NavItem[]) => {
    return children.some(child => isActive(child.href));
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  // Auto-expand active parent items
  useEffect(() => {
    navigationItems.forEach(item => {
      if (item.children && isChildActive(item.children)) {
        setExpandedItems(prev =>
          prev.includes(item.name) ? prev : [...prev, item.name]
        );
      }
    });
  }, [pathname]);

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md muted-glassmorphic shadow-lg text-white hover:bg-white/10"
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-40 w-64 muted-glassmorphic backdrop-blur-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Nova Logo"
                  width={32}
                  height={32}
                  className="transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-pink-400 rounded-full animate-pulse" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">Nova</span>
                <span className="block text-xs text-gray-300">Mentor Portal</span>
              </div>
            </Link>
          </div>

          {/* User info */}
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {session?.user?.name || 'Mentor'}
                </p>
                <p className="text-xs text-gray-300 truncate">
                  {session?.user?.email}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <NavItemComponent key={item.name} item={item} />
            ))}
          </nav>

          {/* Sign out */}
          <div className="p-4 border-t border-white/10">
            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-300 rounded-lg hover:bg-white/5 hover:text-white transition-colors duration-200"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );

  function NavItemComponent({ item }: { item: NavItem }) {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const active = isActive(item.href);
    const childActive = hasChildren && isChildActive(item.children);

    return (
      <div>
        <div
          className={`flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
            active
              ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
              : childActive
              ? 'bg-blue-500/10 text-blue-400'
              : 'text-gray-300 hover:bg-white/5 hover:text-white'
          }`}
        >
          <Link
            href={item.href}
            className="flex items-center flex-1"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <item.icon className="h-5 w-5 mr-3" />
            <span>{item.name}</span>
            {item.badge && item.badge > 0 && (
              <span className="ml-2 bg-pink-500/20 text-pink-300 border border-pink-400/30 text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                {item.badge > 99 ? '99+' : item.badge}
              </span>
            )}
          </Link>
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.name)}
              className="p-1 hover:bg-white/10 rounded"
            >
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-200 ${
                  isExpanded ? 'rotate-180' : ''
                }`}
              />
            </button>
          )}
        </div>

        {/* Children */}
        <AnimatePresence>
          {hasChildren && isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="ml-4 mt-1 space-y-1"
            >
              {item.children!.map((child) => (
                <Link
                  key={child.name}
                  href={child.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${
                    isActive(child.href)
                      ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
                      : 'text-gray-400 hover:bg-white/5 hover:text-white'
                  }`}
                >
                  <child.icon className="h-4 w-4 mr-3" />
                  <span>{child.name}</span>
                </Link>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
}
