'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Briefcase,
  FileText,
  BarChart3,
  Building2,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Plus,
  Users,
  Calendar,
  Eye,
  TrendingUp,
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavItem[];
}

interface EmployerNavbarProps {
  newApplications?: number;
  activeJobs?: number;
}

export function EmployerNavbar({ newApplications = 0, activeJobs = 0 }: EmployerNavbarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/employer',
      icon: LayoutDashboard,
    },
    {
      name: 'Job Management',
      href: '/dashboard/employer/jobs',
      icon: Briefcase,
      badge: activeJobs,
      children: [
        { name: 'All Jobs', href: '/dashboard/employer/jobs', icon: Briefcase },
        { name: 'Post New Job', href: '/jobs/create', icon: Plus },
        { name: 'Job Templates', href: '/dashboard/employer/job-templates', icon: FileText },
      ],
    },
    {
      name: 'Applications',
      href: '/dashboard/employer/applications',
      icon: FileText,
      badge: newApplications,
      children: [
        { name: 'All Applications', href: '/dashboard/employer/applications', icon: FileText },
        { name: 'New Applications', href: '/dashboard/employer/applications?filter=new', icon: Bell },
        { name: 'Interviews', href: '/dashboard/employer/interviews', icon: Calendar },
        { name: 'Hired Candidates', href: '/dashboard/employer/hired', icon: Users },
      ],
    },
    {
      name: 'Analytics',
      href: '/dashboard/employer/analytics',
      icon: BarChart3,
      children: [
        { name: 'Overview', href: '/dashboard/employer/analytics', icon: BarChart3 },
        { name: 'Job Performance', href: '/dashboard/employer/analytics/jobs', icon: TrendingUp },
        { name: 'Profile Views', href: '/dashboard/employer/analytics/profile', icon: Eye },
      ],
    },
    {
      name: 'Company Profile',
      href: '/dashboard/employer/profile',
      icon: Building2,
    },
    {
      name: 'Settings',
      href: '/dashboard/employer/settings',
      icon: Settings,
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard/employer') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const isChildActive = (children: NavItem[]) => {
    return children.some(child => isActive(child.href));
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  // Auto-expand active parent items
  useEffect(() => {
    navigationItems.forEach(item => {
      if (item.children && isChildActive(item.children)) {
        setExpandedItems(prev =>
          prev.includes(item.name) ? prev : [...prev, item.name]
        );
      }
    });
  }, [pathname]);

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md muted-glassmorphic shadow-lg text-white hover:bg-white/10"
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-40 w-64 muted-glassmorphic backdrop-blur-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Nova Logo"
                  width={32}
                  height={32}
                  className="transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-pink-400 rounded-full animate-pulse" />
              </div>
              <span className="text-xl font-bold text-white">Nova</span>
            </Link>
          </div>

          {/* User info */}
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-400/30">
                <Building2 className="h-5 w-5 text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {session?.user?.name || 'Employer'}
                </p>
                <p className="text-xs text-gray-300 truncate">Employer Dashboard</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        isChildActive(item.children)
                          ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
                          : 'text-gray-300 hover:bg-white/5 hover:text-white'
                      }`}
                    >
                      <div className="flex items-center">
                        <item.icon className="mr-3 h-5 w-5" />
                        <span>{item.name}</span>
                        {item.badge && item.badge > 0 && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-500/20 text-pink-300 border border-pink-400/30">
                            {item.badge}
                          </span>
                        )}
                      </div>
                      <ChevronDown
                        className={`h-4 w-4 transition-transform ${
                          expandedItems.includes(item.name) ? 'rotate-180' : ''
                        }`}
                      />
                    </button>
                    <AnimatePresence>
                      {expandedItems.includes(item.name) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="ml-6 mt-1 space-y-1">
                            {item.children.map((child) => (
                              <Link
                                key={child.href}
                                href={child.href}
                                onClick={() => setIsMobileMenuOpen(false)}
                                className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                                  isActive(child.href)
                                    ? 'bg-purple-500/20 text-purple-300 font-medium border border-purple-400/30'
                                    : 'text-gray-400 hover:bg-white/5 hover:text-white'
                                }`}
                              >
                                <child.icon className="mr-3 h-4 w-4" />
                                <span>{child.name}</span>
                              </Link>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
                        : 'text-gray-300 hover:bg-white/5 hover:text-white'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    <span>{item.name}</span>
                    {item.badge && item.badge > 0 && (
                      <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-500/20 text-pink-300 border border-pink-400/30">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Sign out */}
          <div className="p-4 border-t border-white/10">
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-red-500/20 hover:text-red-300 transition-colors"
            >
              <LogOut className="mr-3 h-5 w-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
