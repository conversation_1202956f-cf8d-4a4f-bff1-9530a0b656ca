'use client';

import React from 'react';
import { MentorNavbar } from './MentorNavbar';

interface MentorLayoutProps {
  children: React.ReactNode;
  totalMentees?: number;
  upcomingSessions?: number;
  coursesCreated?: number;
}

export function MentorLayout({
  children,
  totalMentees = 0,
  upcomingSessions = 0,
  coursesCreated = 0
}: MentorLayoutProps) {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <MentorNavbar
        totalMentees={totalMentees}
        upcomingSessions={upcomingSessions}
        coursesCreated={coursesCreated}
      />

      {/* Main content */}
      <div className="lg:ml-64 relative z-10">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
