'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  BookOpen,
  Briefcase,
  Calendar,
  CheckCircle,
  BarChart3,
  Settings,
  UserCheck,
  FileText,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Shield,
  AlertTriangle,
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavItem[];
}

interface AdminNavbarProps {
  pendingApprovals?: number;
  systemAlerts?: number;
}

export function AdminNavbar({ pendingApprovals = 0, systemAlerts = 0 }: AdminNavbarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/admin',
      icon: LayoutDashboard,
    },
    {
      name: 'User Management',
      href: '/dashboard/admin/users',
      icon: Users,
      children: [
        { name: 'All Users', href: '/dashboard/admin/users', icon: Users },
        { name: 'Role Applications', href: '/dashboard/admin/role-applications', icon: UserCheck },
      ],
    },
    {
      name: 'Content Management',
      href: '/dashboard/admin/content',
      icon: BookOpen,
      children: [
        { name: 'Courses', href: '/dashboard/admin/courses', icon: BookOpen },
        { name: 'Resources', href: '/dashboard/admin/resources', icon: FileText },
        { name: 'Content Moderation', href: '/dashboard/admin/content', icon: Shield },
      ],
    },
    {
      name: 'Job Management',
      href: '/dashboard/admin/jobs',
      icon: Briefcase,
    },
    {
      name: 'Event Management',
      href: '/dashboard/admin/events',
      icon: Calendar,
    },
    {
      name: 'Approvals',
      href: '/dashboard/admin/approvals',
      icon: CheckCircle,
      badge: pendingApprovals,
    },
    {
      name: 'Analytics',
      href: '/dashboard/admin/analytics',
      icon: BarChart3,
    },
    {
      name: 'System',
      href: '/dashboard/admin/system',
      icon: Settings,
      children: [
        { name: 'Settings', href: '/dashboard/admin/settings', icon: Settings },
        { name: 'Alerts', href: '/dashboard/admin/alerts', icon: AlertTriangle, badge: systemAlerts },
      ],
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard/admin') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const isParentActive = (item: NavItem) => {
    if (isActive(item.href)) return true;
    return item.children?.some(child => isActive(child.href)) || false;
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  const NavItemComponent = ({ item, level = 0 }: { item: NavItem; level?: number }) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const active = isActive(item.href);
    const parentActive = isParentActive(item);

    return (
      <div>
        <div
          className={`flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
            level > 0 ? 'ml-4' : ''
          } ${
            active
              ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
              : parentActive && hasChildren
              ? 'bg-blue-500/10 text-blue-400'
              : 'text-gray-300 hover:bg-white/5 hover:text-white'
          }`}
        >
          <Link
            href={item.href}
            className="flex items-center flex-1"
            onClick={() => setIsSidebarOpen(false)}
          >
            <item.icon className="h-5 w-5 mr-3" />
            <span>{item.name}</span>
            {item.badge && item.badge > 0 && (
              <span className="ml-2 bg-pink-500/20 text-pink-300 border border-pink-400/30 text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                {item.badge > 99 ? '99+' : item.badge}
              </span>
            )}
          </Link>
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.name)}
              className="p-1 hover:bg-white/10 rounded"
            >
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-200 ${
                  isExpanded ? 'rotate-180' : ''
                }`}
              />
            </button>
          )}
        </div>

        <AnimatePresence>
          {hasChildren && isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="mt-1 space-y-1">
                {item.children?.map((child) => (
                  <NavItemComponent key={child.name} item={child} level={level + 1} />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsSidebarOpen(true)}
          className="p-2 muted-glassmorphic rounded-lg shadow-md hover:bg-white/10"
        >
          <Menu className="h-6 w-6 text-white" />
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-40 w-64 muted-glassmorphic backdrop-blur-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Nova Logo"
                  width={32}
                  height={32}
                  className="transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-pink-400 rounded-full animate-pulse" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">Nova</span>
                <span className="block text-xs text-gray-300">Admin Panel</span>
              </div>
            </Link>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="lg:hidden p-1 hover:bg-white/10 rounded"
            >
              <X className="h-5 w-5 text-gray-300" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <NavItemComponent key={item.name} item={item} />
            ))}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-white/10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-400/30">
                <span className="text-blue-300 font-semibold text-sm">
                  {session?.user?.name?.charAt(0)?.toUpperCase() || 'A'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {session?.user?.name || 'Admin User'}
                </p>
                <p className="text-xs text-gray-300 truncate">
                  {session?.user?.email}
                </p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-red-500/20 hover:text-red-300 rounded-lg transition-colors duration-200"
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
    </>
  );
}
