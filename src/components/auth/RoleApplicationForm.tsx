'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/lib/permissions';

interface RoleApplicationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface ApplicationData {
  reason: string;
  experience?: string;
  qualifications?: string[];
  portfolio?: string;
  references?: {
    name: string;
    email: string;
    relationship: string;
  }[];
  companyInfo?: {
    name: string;
    website: string;
    size: string;
    industry: string;
  };
  partnershipInfo?: {
    organizationName: string;
    organizationType: string;
    region: string;
    website: string;
  };
}

export default function RoleApplicationForm({ onSuccess, onCancel }: RoleApplicationFormProps) {
  const { data: session } = useSession();
  const [selectedRole, setSelectedRole] = useState<UserRole | ''>('');
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    reason: '',
    qualifications: [''],
    references: [{ name: '', email: '', relationship: '' }],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const availableRoles: { value: UserRole; label: string; description: string }[] = [
    {
      value: 'mentor',
      label: 'Mentor',
      description: 'Guide and support students in their learning journey',
    },
    {
      value: 'employer',
      label: 'Employer',
      description: 'Post jobs and recruit talented candidates',
    },
    {
      value: 'partner',
      label: 'Partner Organization',
      description: 'Collaborate on regional content and initiatives',
    },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRole) return;

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/users/role-application', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestedRole: selectedRole,
          applicationData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit application');
      }

      onSuccess?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addQualification = () => {
    setApplicationData(prev => ({
      ...prev,
      qualifications: [...(prev.qualifications || []), ''],
    }));
  };

  const removeQualification = (index: number) => {
    setApplicationData(prev => ({
      ...prev,
      qualifications: prev.qualifications?.filter((_, i) => i !== index),
    }));
  };

  const updateQualification = (index: number, value: string) => {
    setApplicationData(prev => ({
      ...prev,
      qualifications: prev.qualifications?.map((qual, i) => i === index ? value : qual),
    }));
  };

  const addReference = () => {
    setApplicationData(prev => ({
      ...prev,
      references: [...(prev.references || []), { name: '', email: '', relationship: '' }],
    }));
  };

  const removeReference = (index: number) => {
    setApplicationData(prev => ({
      ...prev,
      references: prev.references?.filter((_, i) => i !== index),
    }));
  };

  const updateReference = (index: number, field: string, value: string) => {
    setApplicationData(prev => ({
      ...prev,
      references: prev.references?.map((ref, i) => 
        i === index ? { ...ref, [field]: value } : ref
      ),
    }));
  };

  return (
    <div className="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Apply for Role Upgrade</h2>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Role Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Role
          </label>
          <div className="space-y-3">
            {availableRoles.map((role) => (
              <div key={role.value} className="flex items-start">
                <input
                  type="radio"
                  id={role.value}
                  name="role"
                  value={role.value}
                  checked={selectedRole === role.value}
                  onChange={(e) => setSelectedRole(e.target.value as UserRole)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="ml-3">
                  <label htmlFor={role.value} className="block text-sm font-medium text-gray-900">
                    {role.label}
                  </label>
                  <p className="text-sm text-gray-500">{role.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {selectedRole && (
          <>
            {/* Reason */}
            <div>
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                Why do you want this role? *
              </label>
              <textarea
                id="reason"
                rows={4}
                required
                value={applicationData.reason}
                onChange={(e) => setApplicationData(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Explain your motivation and how you plan to contribute..."
              />
            </div>

            {/* Experience */}
            <div>
              <label htmlFor="experience" className="block text-sm font-medium text-gray-700 mb-2">
                Relevant Experience
              </label>
              <textarea
                id="experience"
                rows={3}
                value={applicationData.experience || ''}
                onChange={(e) => setApplicationData(prev => ({ ...prev, experience: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe your relevant experience..."
              />
            </div>

            {/* Qualifications */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Qualifications & Certifications
              </label>
              {applicationData.qualifications?.map((qual, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <input
                    type="text"
                    value={qual}
                    onChange={(e) => updateQualification(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter qualification..."
                  />
                  {applicationData.qualifications!.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeQualification(index)}
                      className="px-3 py-2 text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addQualification}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                + Add Qualification
              </button>
            </div>

            {/* Portfolio */}
            <div>
              <label htmlFor="portfolio" className="block text-sm font-medium text-gray-700 mb-2">
                Portfolio/Website URL
              </label>
              <input
                type="url"
                id="portfolio"
                value={applicationData.portfolio || ''}
                onChange={(e) => setApplicationData(prev => ({ ...prev, portfolio: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://your-portfolio.com"
              />
            </div>

            {/* Role-specific fields */}
            {selectedRole === 'employer' && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-md">
                <h3 className="font-medium text-gray-900">Company Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={applicationData.companyInfo?.name || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, name: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Website
                    </label>
                    <input
                      type="url"
                      value={applicationData.companyInfo?.website || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, website: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Size
                    </label>
                    <select
                      value={applicationData.companyInfo?.size || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, size: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select size</option>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201-1000">201-1000 employees</option>
                      <option value="1000+">1000+ employees</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Industry
                    </label>
                    <input
                      type="text"
                      value={applicationData.companyInfo?.industry || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, industry: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Space Technology, Aerospace"
                    />
                  </div>
                </div>
              </div>
            )}

            {selectedRole === 'partner' && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-md">
                <h3 className="font-medium text-gray-900">Organization Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Organization Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={applicationData.partnershipInfo?.organizationName || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        partnershipInfo: { ...prev.partnershipInfo, organizationName: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Organization Type
                    </label>
                    <select
                      value={applicationData.partnershipInfo?.organizationType || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        partnershipInfo: { ...prev.partnershipInfo, organizationType: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select type</option>
                      <option value="university">University</option>
                      <option value="research">Research Institution</option>
                      <option value="government">Government Agency</option>
                      <option value="ngo">NGO/Non-profit</option>
                      <option value="corporate">Corporate</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Region/Country
                    </label>
                    <input
                      type="text"
                      value={applicationData.partnershipInfo?.region || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        partnershipInfo: { ...prev.partnershipInfo, region: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Kenya, East Africa"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Website
                    </label>
                    <input
                      type="url"
                      value={applicationData.partnershipInfo?.website || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        partnershipInfo: { ...prev.partnershipInfo, website: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* References */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                References (Optional)
              </label>
              {applicationData.references?.map((ref, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-3 p-3 border border-gray-200 rounded-md">
                  <input
                    type="text"
                    placeholder="Name"
                    value={ref.name}
                    onChange={(e) => updateReference(index, 'name', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="email"
                    placeholder="Email"
                    value={ref.email}
                    onChange={(e) => updateReference(index, 'email', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Relationship"
                      value={ref.relationship}
                      onChange={(e) => updateReference(index, 'relationship', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    {applicationData.references!.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeReference(index)}
                        className="px-3 py-2 text-red-600 hover:text-red-800"
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={addReference}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                + Add Reference
              </button>
            </div>
          </>
        )}

        {/* Submit Buttons */}
        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={!selectedRole || isSubmitting}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Application'}
          </button>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
